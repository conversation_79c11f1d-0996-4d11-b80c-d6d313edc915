# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述
这是一个基于Spring Boot的开发数据分析系统，用于分析开发团队的各项指标，包括代码提交统计、Bug率统计、工时分布等。

## 构建和运行命令

### 构建项目
```bash
mvn clean package
```

### 运行项目
```bash
java -jar target/cursor-analysis-0.0.1-SNAPSHOT.jar
```

### 使用启动脚本运行
```bash
./start.sh
```

### 开发环境启动
```bash
mvn spring-boot:run
```

## 技术架构

### 后端技术栈
- Spring Boot 2.7.9 (Java 8)
- MyBatis
- Spring Security + LDAP认证
- MySQL (双数据源配置)
- Thymeleaf模板引擎
- EasyExcel

### 前端技术栈
- Thymeleaf模板
- ECharts图表库
- jQuery + Select2
- Bootstrap响应式布局

### 双数据源架构
- **主数据源(primary)**: dev_effe数据库 - 存储开发效能数据
- **次数据源(secondary)**: test_hongdong数据库 - 存储测试和配置数据
- 实体类按数据源分离到`entity/primary`和`entity/secondary`包
- Mapper接口和XML分别在`mapper/primary`和`mapper/secondary`

## 核心业务模块

### 开发数据分析
- 代码提交统计（按月/按周）
- Bug率统计和分析
- 工时分布和统计
- 产线Bug统计

### 项目统计分析
- 项目验收数据分析
- 项目统计数据服务
- 灰度数据管理

### 测试执行统计
- 测试用例执行情况
- 测试结果统计分析

## 关键配置

### 数据库连接
- 主库: ******************************************
- 次库: ***********************************************
- 用户名/密码: root/mysql

### 应用配置
- 端口: 8083
- 日志路径: /data/app/cursor-analysis/logs/
- LDAP服务器: ldap://192.168.230.11:389

### 默认管理员账号
- 用户名: admin
- 密码: Howbuy@2025

## 开发规范要点

### 代码注释规范
- 作者统一为: `<AUTHOR>
- 时间格式: `@date 2025-08-20 15:15:40`
- 通过 `date "+%Y-%m-%d %H:%M:%S"` 命令获取当前时间
- 不要修改已有注释的@author和@date值

### 实体类规范
- 使用`@Setter/@Getter`，禁止使用`@Data`注解
- VO/PO/DTO/BO等实体类严格按此规范
- 禁止使用`BeanUtils.copyProperties`进行对象属性复制

### 项目规范
- 严格遵循`.cursor/rules/`中定义的开发规范
- 单个文件不超过1000行
- 优先使用构造函数注入而非字段注入
- 任务完成后无需执行测试

## 目录结构说明

### 核心包结构
- `config/` - 配置类(数据源、安全、异常处理等)
- `controller/` - REST控制器
- `service/` - 服务层，接口与实现分离
- `entity/primary|secondary/` - 实体类，按数据源分离
- `mapper/primary|secondary/` - MyBatis接口，按数据源分离
- `dto/` - 数据传输对象
- `exception/` - 自定义异常

### 前端资源
- `templates/` - Thymeleaf模板文件
- `templates/layout/` - 布局模板(base.html, public-base.html)
- `static/` - 静态资源文件
- `mapper/` - MyBatis XML映射文件

## 关键特性

### 安全认证
- 集成企业LDAP认证
- Spring Security配置
- 支持本地用户和LDAP用户登录

### 数据可视化
- ECharts图表展示各类统计数据
- 响应式图表设计
- 支持数据导出(Excel)

### 多数据源事务管理
- 主次数据源独立事务管理
- 数据源路由和切换
- MyBatis多数据源配置

## 部署环境
- 生产环境路径: /data/app/cursor-analysis/
- JVM参数配置在start.sh中
- 支持JMX监控(端口9999)
- 日志文件按日期滚动存储