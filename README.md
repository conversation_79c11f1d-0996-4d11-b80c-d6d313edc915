# 开发数据分析系统 (Cursor Analysis)

## 项目概述
本项目是一个基于Spring Boot的开发数据分析系统，主要用于分析和展示开发团队的各项指标，包括代码提交统计、Bug率统计、工时分布等。系统通过直观的图表展示这些数据，帮助团队管理者更好地了解团队成员的工作情况和项目进展。

## 主要功能
- **代码提交统计**：展示团队成员的代码提交量
- **按周代码统计**：按周展示代码提交情况
- **Bug率统计**：分析团队成员的Bug率
- **工时分布统计**：展示团队成员的工时分布情况
- **工时统计**：统计团队成员的工时数据
- **产线Bug统计**：按产品线统计Bug情况
- **LDAP认证**：支持企业LDAP认证登录

## 技术栈
- **后端**：
  - Spring Boot 2.7.9
  - MyBatis
  - Spring Security
  - LDAP认证
  - MySQL数据库
  - Thymeleaf模板引擎
  
- **前端**：
  - ECharts图表库
  - jQuery
  - Select2
  - 响应式布局

## 项目结构
```
src/main/
├── java/com/example/demo/
│   ├── config/                 # 配置类
│   │   ├── DataSourceConfig.java         # 数据源配置
│   │   ├── SecurityConfig.java           # 安全配置
│   │   └── CustomLdapAuthenticationProvider.java  # LDAP认证提供者
│   ├── controller/             # 控制器
│   │   ├── DevBugsController.java        # Bug统计控制器
│   │   ├── DevCommitsController.java     # 代码提交控制器
│   │   └── ...
│   ├── entity/                 # 实体类
│   │   ├── primary/                      # 主数据源实体
│   │   └── secondary/                    # 次数据源实体
│   ├── mapper/                 # MyBatis Mapper接口
│   │   ├── primary/                      # 主数据源Mapper
│   │   └── secondary/                    # 次数据源Mapper
│   ├── service/                # 服务层
│   │   ├── DevBugsService.java           # Bug统计服务
│   │   ├── DevCommitsService.java        # 代码提交服务
│   │   └── ...
│   └── DemoApplication.java    # 应用入口
└── resources/
    ├── mapper/                 # MyBatis XML映射文件
    │   ├── primary/                      # 主数据源映射
    │   └── secondary/                    # 次数据源映射
    ├── templates/              # Thymeleaf模板
    │   ├── layout/                       # 布局模板
    │   ├── bugs-chart.html               # Bug统计页面
    │   └── ...
    └── application.yml         # 应用配置文件
```

## 数据库设计
系统使用双数据源配置：
- **主数据源**：存储开发数据分析相关的表
- **次数据源**：存储其他业务数据

主要表结构包括：
- dev_bugs_of_commits_monthly：开发人员Bug统计
- dev_commits_monthly：开发人员代码提交统计
- dev_work_time_spent：开发人员工时统计
- base_dever_info：开发人员基本信息

## 安装与运行
1. 克隆项目到本地
2. 配置数据库连接（修改application.yml）
3. 配置LDAP连接（如需使用LDAP认证）
4. 执行以下命令构建并运行项目：

```bash
# 构建项目
mvn clean package

# 运行项目
java -jar target/cursor-analysis-0.0.1-SNAPSHOT.jar
```

或者使用提供的启动脚本：
```bash
./start.sh
```

## 访问系统
系统默认运行在8083端口，访问地址：http://localhost:8083

默认管理员账号：
- 用户名：admin
- 密码：Howbuy@2025

## 开发团队
- 项目负责人：hongdong.xie
- 开发日期：2025-03-13 09:29:10 (UTC+8) 