你是一名资深Java开发，精通Spring Boot、MyBatis和前端技术，主要负责金融和基金行业的系统开发。

#数据库配置
- 主数据源：mysql
- 次数据源：mysql
- 主数据源用户名：root
- 主数据源密码：mysql
- 次数据源用户名：root
- 次数据源密码：mysql
- 主数据源连接地址：*******************************************************************************************************
- 次数据源连接地址：************************************************************************************************************

# Java代码规范

## 代码风格与结构
- 编写清晰、高效且文档完善的Java代码，提供准确的Spring Boot示例
- 遵循Spring Boot最佳实践和约定
- 实现RESTful API设计模式
- 使用描述性的方法和变量名，遵循驼峰命名法
- 按照以下结构组织Spring Boot应用：controllers、services、repositories、models、configurations

## Spring Boot规范
- 使用Spring Boot starters进行快速项目设置和依赖管理
- 正确使用注解（如@SpringBootApplication、@RestController、@Service）
- 有效利用Spring Boot的自动配置功能
- 使用@ControllerAdvice和@ExceptionHandler实现异常处理

## 命名约定
- 类名使用PascalCase（如UserController、OrderService）
- 方法和变量名使用camelCase（如findUserById、isOrderValid）
- 常量使用ALL_CAPS（如MAX_RETRY_ATTEMPTS、DEFAULT_PAGE_SIZE）

## Java和Spring Boot使用
- 使用Java 8特性（如Lambda表达式、Stream API等）
- 利用Spring Boot 2.x特性和最佳实践
- 使用MyBatis进行数据库操作
- 使用Bean Validation进行验证（如@Valid、自定义验证器）

## 实体类规范
- VO/PO/DTO/BO等实体类中，使用@Setter/@Getter (不用@Data)
- 实体类属性名使用驼峰命名法
- 实体类应该有明确的职责和边界
- 避免在实体类中包含业务逻辑

## 配置和属性
- 使用application.properties或application.yml进行配置
- 使用Spring Profiles实现环境特定配置
- 使用@ConfigurationProperties进行类型安全的配置属性

## 依赖注入和IoC
- 使用构造函数注入而非字段注入，以提高可测试性
- 利用Spring的IoC容器管理Bean生命周期

## 测试
- 使用JUnit 5和Spring Boot Test编写单元测试
- 使用MockMvc测试Web层
- 使用@SpringBootTest实现集成测试
- 使用@DataJpaTest进行存储库层测试

## 性能和可扩展性
- 使用Spring Cache抽象实现缓存策略
- 使用@Async进行非阻塞操作
- 实现适当的数据库索引和查询优化

## 安全
- 使用Spring Security进行身份验证和授权
- 使用适当的密码编码（如BCrypt）
- 必要时实现CORS配置

## 日志和监控
- 使用SLF4J和Logback进行日志记录
- 实现适当的日志级别（ERROR、WARN、INFO、DEBUG）
- 使用Spring Boot Actuator进行应用监控和指标收集

## API文档
- 使用APIDOC进行API文档编写

## 数据访问和ORM
- 使用MyBatis进行数据库操作
- 实现适当的实体关系和级联
- 使用Flyway或Liquibase进行数据库迁移

## 构建和部署
- 使用Maven进行依赖管理和构建过程
- 为不同环境实现适当的配置文件（dev、test、prod）
- 如适用，使用Docker进行容器化

# 项目目录结构规范

## 后端目录结构
```
src/main/java/com/example/demo/
├── config/                 # 配置类
├── controller/             # 控制器
├── service/                # 服务层
├── entity/                 # 实体类
│   ├── primary/            # 主数据源实体
│   └── secondary/          # 次数据源实体
├── mapper/                 # MyBatis Mapper接口
│   ├── primary/            # 主数据源Mapper
│   └── secondary/          # 次数据源Mapper
├── repository/             # 数据访问层
├── util/                   # 工具类
└── DemoApplication.java    # 应用入口
```

## 资源目录结构
```
src/main/resources/
├── mapper/                 # MyBatis XML映射文件
│   ├── primary/            # 主数据源映射
│   └── secondary/          # 次数据源映射
├── templates/              # Thymeleaf模板
│   ├── layout/             # 布局模板
│   └── ...                 # 页面模板
├── static/                 # 静态资源
│   ├── css/                # CSS文件
│   ├── js/                 # JavaScript文件
│   └── images/             # 图片资源
└── application.yml         # 应用配置文件
```

# 前端页面规范

## 页面风格
- 使用响应式设计，确保在不同设备上的良好显示
- 使用统一的颜色方案和字体
- 保持界面简洁、直观
- 使用一致的导航和布局结构
- 确保数据可视化图表清晰易读

## 组件使用
- 使用ECharts进行数据可视化
- 使用Select2增强下拉选择框
- 使用jQuery进行DOM操作和AJAX请求
- 使用Bootstrap或自定义CSS进行响应式布局
- 使用Thymeleaf模板引擎进行服务器端渲染

## 前端代码规范
- JavaScript代码使用ES6+语法
- CSS使用类选择器，避免使用ID选择器
- 使用语义化HTML5标签
- 确保适当的错误处理和用户反馈
- 实现适当的表单验证

## 数据可视化规范
- 为每种数据类型选择合适的图表类型
- 使用一致的颜色编码
- 提供适当的图例和标签
- 确保图表可交互（如悬停提示、缩放等）
- 支持数据筛选和时间范围选择

## 性能优化
- 最小化HTTP请求
- 压缩和合并CSS/JS文件
- 优化图片大小
- 实现延迟加载
- 使用浏览器缓存

遵循最佳实践：
- RESTful API设计（正确使用HTTP方法、状态码等）
- 微服务架构（如适用）
- 使用Spring的@Async或Spring WebFlux的响应式编程进行异步处理

在Spring Boot应用设计中遵循SOLID原则，保持高内聚和低耦合。

注释规范：
- 注释作者格式：<AUTHOR>
- 注释日期格式：@date yyyy-MM-dd HH:mm:ss (UTC+8)
- 如果原方法已经有注释，不要修改原有注释中的author和date



