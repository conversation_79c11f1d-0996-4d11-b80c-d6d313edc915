#!/bin/bash
export LANG=zh_CN.UTF-8
export JRE_HOME=${JAVA_HOME}/jre
export PATH=$JAVA_HOME/bin:$JRE_HOME/bin:$PATH

 #!/bin/bash
APP_PATH=/data/app/cursor-analysis
LOG_PATH=/data/app/cursor-analysis
APP_RESOURCE=/data/app/cursor-analysis/conf
LIB_DIR=/data/app/cursor-analysis/lib
LIB_JARS=`ls $LIB_DIR|grep .jar|awk '{print "'$LIB_DIR'/"$0}'|tr "\n" ":"`
cd ${LOG_PATH}
nohup java -server \
 -Xms1024m \
 -Xmx1024m \
 -XX:PermSize=128m \
 -XX:MaxPermSize=128M \
 -XX:-OmitStackTraceInFastThrow \
 -XX:+PrintHeapAtGC \
 -XX:+PrintGCDateStamps \
 -XX:+PrintGCDetails \
 -XX:+PrintGCApplicationStoppedTime \
 -XX:+PrintReferenceGC \
 -XX:+PrintTenuringDistribution \
 -Dcom.sun.management.jmxremote \
 -Dcom.sun.management.jmxremote.ssl=false \
 -Dcom.sun.management.jmxremote.authenticate=false \
 -Dcom.sun.management.jmxremote.port=9999 \
 -Djava.util.Arrays.useLegacyMergeSort=true \
 -Xbootclasspath/a:${APP_RESOURCE} \
 -classpath $APP_RESOURCE:$LIB_JARS com.example.demo.DemoApplication >>${LOG_PATH}/start-`date +%F`.log &

