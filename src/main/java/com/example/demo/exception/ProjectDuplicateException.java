package com.example.demo.exception;

/**
 * 项目重复导入异常
 * 用于标识项目数据已存在，需要用户确认是否覆盖
 *
 * <AUTHOR>
 * @date 2025-07-10 09:22:01
 */
public class ProjectDuplicateException extends Exception {
    
    private final String projectName;
    private final int existingRecordCount;
    
    public ProjectDuplicateException(String projectName, int existingRecordCount) {
        super("项目【" + projectName + "】已存在 " + existingRecordCount + " 条数据，是否要覆盖现有数据？");
        this.projectName = projectName;
        this.existingRecordCount = existingRecordCount;
    }
    
    public String getProjectName() {
        return projectName;
    }
    
    public int getExistingRecordCount() {
        return existingRecordCount;
    }
}
