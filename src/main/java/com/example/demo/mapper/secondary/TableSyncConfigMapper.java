package com.example.demo.mapper.secondary;

import com.example.demo.entity.secondary.TableSyncConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 表同步配置Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-30 07:44:32
 */
@Mapper
public interface TableSyncConfigMapper {
    /**
     * 根据ID查询表同步配置
     *
     * @param id 主键ID
     * @return 表同步配置信息
     */
    TableSyncConfig selectById(@Param("id") Long id);

    /**
     * 查询所有有效的表同步配置
     *
     * @return 表同步配置列表
     */
    List<TableSyncConfig> selectAllValid();

    /**
     * 根据源数据库和状态查询表同步配置
     *
     * @param sourceDb 源数据库
     * @param status 状态
     * @return 表同步配置列表
     */
    List<TableSyncConfig> selectBySourceDbAndStatus(@Param("sourceDb") String sourceDb, @Param("status") Integer status);

    /**
     * 插入表同步配置
     *
     * @param config 表同步配置信息
     * @return 影响行数
     */
    int insert(TableSyncConfig config);

    /**
     * 更新表同步配置
     *
     * @param config 表同步配置信息
     * @return 影响行数
     */
    int update(TableSyncConfig config);

    /**
     * 根据ID删除表同步配置
     *
     * @param id 主键ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
} 