package com.example.demo.mapper.primary;

import com.example.demo.entity.primary.DevCommitsWeekly;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface DevCommitsWeeklyMapper {
    List<DevCommitsWeekly> findByConditions(String team, List<String> usernames, String startWeek, String endWeek);
    List<String> findDistinctWeeks();
    List<String> findUsernamesByTeam(String team);
    List<String> findDistinctTeams();
} 