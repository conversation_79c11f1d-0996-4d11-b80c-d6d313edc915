package com.example.demo.mapper.primary;

import com.example.demo.entity.primary.DevCommitsMonthly;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface DevCommitsMonthlyMapper {
    List<DevCommitsMonthly> findAll();
    List<DevCommitsMonthly> findByUsernames(List<String> usernames);
    List<DevCommitsMonthly> findByTeamAndDateRange(String team, String startMonth, String endMonth);
    List<String> findDistinctUsernames();
    List<String> findDistinctMonthly();
    List<String> findDistinctTeams();
    List<String> findUsernamesByTeam(String team);
} 