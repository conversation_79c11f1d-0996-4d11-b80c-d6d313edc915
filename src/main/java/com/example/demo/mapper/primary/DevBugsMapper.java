package com.example.demo.mapper.primary;

import com.example.demo.entity.primary.DevBugsOfCommits;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface DevBugsMapper {
    List<DevBugsOfCommits> findByConditions(String team, List<String> usernames, String startMonth, String endMonth, String mode);
    List<String> findDistinctMonths();
    List<String> findUsernamesByTeam(String team);
} 