package com.example.demo.mapper.primary;

import com.example.demo.dto.PersonalCommitStatsRequestDTO;
import com.example.demo.dto.PersonalCommitStatsResponseDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 个人代码提交统计Mapper接口
 * <AUTHOR>
 * @date 2025-08-23 10:15:55
 */
@Mapper
public interface PersonalCommitStatsMapper {
    
    /**
     * 查询个人年度统计数据
     */
    List<PersonalCommitStatsResponseDTO> selectPersonalYearlyStats(@Param("request") PersonalCommitStatsRequestDTO request);
    
    /**
     * 查询个人半年度统计数据
     */
    List<PersonalCommitStatsResponseDTO> selectPersonalHalfYearlyStats(@Param("request") PersonalCommitStatsRequestDTO request);
    
    /**
     * 查询个人季度统计数据
     */
    List<PersonalCommitStatsResponseDTO> selectPersonalQuarterlyStats(@Param("request") PersonalCommitStatsRequestDTO request);
    
    /**
     * 查询Top N开发者排名数据
     */
    List<PersonalCommitStatsResponseDTO> selectTopDevelopersRanking(@Param("request") PersonalCommitStatsRequestDTO request);
    
    /**
     * 查询所有开发者用户名
     */
    List<String> selectAllDevelopers();
    
    /**
     * 查询指定团队的开发者用户名
     */
    List<String> selectDevelopersByTeams(@Param("teams") List<String> teams);
}