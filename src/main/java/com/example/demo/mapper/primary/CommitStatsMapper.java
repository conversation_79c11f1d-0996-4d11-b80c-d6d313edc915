package com.example.demo.mapper.primary;

import com.example.demo.dto.CommitStatsRequestDTO;
import com.example.demo.dto.CommitStatsResponseDTO;
import com.example.demo.dto.CommitStatsSummaryDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代码提交统计Mapper接口
 * <AUTHOR>
 * @date 2025-08-20 16:01:37
 */
@Mapper
public interface CommitStatsMapper {
    
    /**
     * 查询年度统计数据
     */
    List<CommitStatsResponseDTO> selectYearlyStats(@Param("request") CommitStatsRequestDTO request);
    
    /**
     * 查询半年度统计数据
     */
    List<CommitStatsResponseDTO> selectHalfYearlyStats(@Param("request") CommitStatsRequestDTO request);
    
    /**
     * 查询季度统计数据
     */
    List<CommitStatsResponseDTO> selectQuarterlyStats(@Param("request") CommitStatsRequestDTO request);
    
    /**
     * 查询团队年度统计数据
     */
    List<CommitStatsResponseDTO> selectTeamYearlyStats(@Param("request") CommitStatsRequestDTO request);
    
    /**
     * 查询团队半年度统计数据
     */
    List<CommitStatsResponseDTO> selectTeamHalfYearlyStats(@Param("request") CommitStatsRequestDTO request);
    
    /**
     * 查询团队季度统计数据
     */
    List<CommitStatsResponseDTO> selectTeamQuarterlyStats(@Param("request") CommitStatsRequestDTO request);
    
    /**
     * 查询统计汇总数据
     */
    List<CommitStatsSummaryDTO> selectStatsSummary(@Param("request") CommitStatsRequestDTO request);
    
    /**
     * 查询所有可用年份
     */
    List<Integer> selectAvailableYears();
    
    /**
     * 查询所有团队信息
     */
    List<String> selectAllTeams();
}