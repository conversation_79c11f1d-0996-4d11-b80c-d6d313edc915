package com.example.demo.mapper.primary;

import com.example.demo.entity.primary.ProjectAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 项目分析Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Mapper
public interface ProjectAnalysisMapper {

    /**
     * 根据项目名称查询项目分析报告
     *
     * @param projectName 项目名称
     * @return 项目分析报告
     */
    ProjectAnalysis findByProjectName(@Param("projectName") String projectName);

    /**
     * 插入项目分析报告
     *
     * @param projectAnalysis 项目分析报告
     * @return 影响行数
     */
    int insert(ProjectAnalysis projectAnalysis);

    /**
     * 更新项目分析报告
     *
     * @param projectAnalysis 项目分析报告
     * @return 影响行数
     */
    int update(ProjectAnalysis projectAnalysis);

    /**
     * 根据项目名称删除项目分析报告
     *
     * @param projectName 项目名称
     * @return 影响行数
     */
    int deleteByProjectName(@Param("projectName") String projectName);
}
