package com.example.demo.mapper.primary;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目统计数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Mapper
public interface ProjectStatsDataMapper {

    /**
     * 获取项目统计数据列表
     *
     * @param projectName 项目名称（模糊查询）
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目统计数据列表
     */
    List<Map<String, Object>> getProjectStatsData(@Param("projectName") String projectName,
                                                  @Param("onlineDateStart") String onlineDateStart,
                                                  @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取项目开发问题分类统计
     *
     * @param projectName 项目名称
     * @return 开发问题分类统计
     */
    List<Map<String, Object>> getProjectDevIssueCategories(@Param("projectName") String projectName);

    /**
     * 获取项目测试问题分类统计
     *
     * @param projectName 项目名称
     * @return 测试问题分类统计
     */
    List<Map<String, Object>> getProjectTestIssueCategories(@Param("projectName") String projectName);

    /**
     * 检查项目是否有分析报告
     *
     * @param projectName 项目名称
     * @return 是否有分析报告
     */
    Integer checkProjectAnalysisExists(@Param("projectName") String projectName);

    /**
     * 获取项目分析报告内容
     *
     * @param projectName 项目名称
     * @return 分析报告内容
     */
    String getProjectAnalysisContent(@Param("projectName") String projectName);
}
