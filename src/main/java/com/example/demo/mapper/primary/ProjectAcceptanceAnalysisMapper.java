package com.example.demo.mapper.primary;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目验收数据分析Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Mapper
public interface ProjectAcceptanceAnalysisMapper {

    /**
     * 获取所有项目名列表（去重）
     *
     * @return 项目名列表
     */
    List<String> findDistinctProjectNames();

    /**
     * 根据上线日期获取最新的项目名
     *
     * @return 最新项目名
     */
    String findLatestProjectByOnlineDate();

    /**
     * 获取开发问题分类统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 开发问题分类统计数据
     */
    List<Map<String, Object>> getDevIssueCategoryStats(@Param("projectName") String projectName,
                                                       @Param("grayDateStart") String grayDateStart,
                                                       @Param("grayDateEnd") String grayDateEnd,
                                                       @Param("onlineDateStart") String onlineDateStart,
                                                       @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取测试问题分类统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 测试问题分类统计数据
     */
    List<Map<String, Object>> getTestIssueCategoryStats(@Param("projectName") String projectName,
                                                        @Param("grayDateStart") String grayDateStart,
                                                        @Param("grayDateEnd") String grayDateEnd,
                                                        @Param("onlineDateStart") String onlineDateStart,
                                                        @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取测试人员Bug数统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 测试人员Bug数统计数据
     */
    List<Map<String, Object>> getTesterBugStats(@Param("projectName") String projectName,
                                               @Param("grayDateStart") String grayDateStart,
                                               @Param("grayDateEnd") String grayDateEnd,
                                               @Param("onlineDateStart") String onlineDateStart,
                                               @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取开发人员Bug数统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 开发人员Bug数统计数据
     */
    List<Map<String, Object>> getDeveloperBugStats(@Param("projectName") String projectName,
                                                   @Param("grayDateStart") String grayDateStart,
                                                   @Param("grayDateEnd") String grayDateEnd,
                                                   @Param("onlineDateStart") String onlineDateStart,
                                                   @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取开发人员问题分类详细统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 开发人员问题分类详细统计数据
     */
    List<Map<String, Object>> getDeveloperIssueDetailStats(@Param("projectName") String projectName,
                                                           @Param("grayDateStart") String grayDateStart,
                                                           @Param("grayDateEnd") String grayDateEnd,
                                                           @Param("onlineDateStart") String onlineDateStart,
                                                           @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取测试人员问题分类详细统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 测试人员问题分类详细统计数据
     */
    List<Map<String, Object>> getTesterIssueDetailStats(@Param("projectName") String projectName,
                                                        @Param("grayDateStart") String grayDateStart,
                                                        @Param("grayDateEnd") String grayDateEnd,
                                                        @Param("onlineDateStart") String onlineDateStart,
                                                        @Param("onlineDateEnd") String onlineDateEnd);
}
