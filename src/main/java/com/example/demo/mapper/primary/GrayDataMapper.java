package com.example.demo.mapper.primary;

import com.example.demo.entity.primary.GrayData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 灰度数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-09 16:54:24
 */
@Mapper
public interface GrayDataMapper {
    
    /**
     * 插入灰度数据
     *
     * @param grayData 灰度数据
     * @return 影响行数
     */
    int insert(GrayData grayData);
    
    /**
     * 批量插入灰度数据
     *
     * @param grayDataList 灰度数据列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<GrayData> grayDataList);
    
    /**
     * 根据条件查询灰度数据
     *
     * @param projectName 项目名（支持模糊查询）
     * @param acceptanceEnv 验收环境
     * @return 灰度数据列表
     */
    List<GrayData> findByConditions(@Param("projectName") String projectName, 
                                   @Param("acceptanceEnv") String acceptanceEnv);
    
    /**
     * 根据项目名查询数据数量
     *
     * @param projectName 项目名
     * @return 数据数量
     */
    int countByProjectName(@Param("projectName") String projectName);
    
    /**
     * 获取所有项目名列表（去重）
     *
     * @return 项目名列表
     */
    List<String> findDistinctProjectNames();
    
    /**
     * 获取所有验收环境列表（去重）
     *
     * @return 验收环境列表
     */
    List<String> findDistinctAcceptanceEnvs();
    
    /**
     * 根据项目名删除数据
     *
     * @param projectName 项目名
     * @return 影响行数
     */
    int deleteByProjectName(@Param("projectName") String projectName);
}
