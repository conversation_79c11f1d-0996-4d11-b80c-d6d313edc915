package com.example.demo.mapper.primary;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 项目统计分析Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Mapper
public interface ProjectStatsAnalysisMapper {

    /**
     * 获取所有项目名列表（去重）
     *
     * @return 项目名列表
     */
    List<String> findDistinctProjectNames();

    /**
     * 获取最近半年内上线的项目列表
     *
     * @return 最近半年内上线的项目名列表
     */
    List<String> findRecentProjectsByOnlineDate();

    /**
     * 获取项目基础统计数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目基础统计数据
     */
    List<Map<String, Object>> getProjectBasicStats(@Param("projectNames") List<String> projectNames,
                                                   @Param("grayDateStart") String grayDateStart,
                                                   @Param("grayDateEnd") String grayDateEnd,
                                                   @Param("onlineDateStart") String onlineDateStart,
                                                   @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取项目开发问题分类统计数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目开发问题分类统计数据
     */
    List<Map<String, Object>> getProjectDevIssueStats(@Param("projectNames") List<String> projectNames,
                                                      @Param("grayDateStart") String grayDateStart,
                                                      @Param("grayDateEnd") String grayDateEnd,
                                                      @Param("onlineDateStart") String onlineDateStart,
                                                      @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取项目测试问题分类统计数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目测试问题分类统计数据
     */
    List<Map<String, Object>> getProjectTestIssueStats(@Param("projectNames") List<String> projectNames,
                                                       @Param("grayDateStart") String grayDateStart,
                                                       @Param("grayDateEnd") String grayDateEnd,
                                                       @Param("onlineDateStart") String onlineDateStart,
                                                       @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取项目开发人员Bug数统计数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目开发人员Bug数统计数据
     */
    List<Map<String, Object>> getProjectDeveloperBugStats(@Param("projectNames") List<String> projectNames,
                                                          @Param("grayDateStart") String grayDateStart,
                                                          @Param("grayDateEnd") String grayDateEnd,
                                                          @Param("onlineDateStart") String onlineDateStart,
                                                          @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取项目测试人员Bug数统计数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目测试人员Bug数统计数据
     */
    List<Map<String, Object>> getProjectTesterBugStats(@Param("projectNames") List<String> projectNames,
                                                       @Param("grayDateStart") String grayDateStart,
                                                       @Param("grayDateEnd") String grayDateEnd,
                                                       @Param("onlineDateStart") String onlineDateStart,
                                                       @Param("onlineDateEnd") String onlineDateEnd);

    /**
     * 获取项目质量指标统计数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目质量指标统计数据
     */
    List<Map<String, Object>> getProjectQualityIndicators(@Param("projectNames") List<String> projectNames,
                                                          @Param("grayDateStart") String grayDateStart,
                                                          @Param("grayDateEnd") String grayDateEnd,
                                                          @Param("onlineDateStart") String onlineDateStart,
                                                          @Param("onlineDateEnd") String onlineDateEnd);
}
