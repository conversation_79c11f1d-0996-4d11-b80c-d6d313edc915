package com.example.demo.mapper.primary;

import com.example.demo.entity.primary.DevWorkTimeSpent;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

@Mapper
public interface DevWorkTimeMapper {
    List<DevWorkTimeSpent> findByConditions(String team, List<String> usernames, 
                                          String startMonth, String endMonth);
    List<String> findDistinctUsers(String team);
    List<String> findDistinctTeams();
} 