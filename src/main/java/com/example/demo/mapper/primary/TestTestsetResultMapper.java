package com.example.demo.mapper.primary;

import com.example.demo.entity.primary.TestExecutionStats;
import com.example.demo.entity.primary.TestTestsetResult;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TestTestsetResultMapper {
    int deleteByPrimaryKey(Long id);

    int insert(TestTestsetResult record);

    int insertSelective(TestTestsetResult record);

    TestTestsetResult selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TestTestsetResult record);

    int updateByPrimaryKey(TestTestsetResult record);
    
    /**
     * 查询自动化测试执行统计数据
     * @param startDate 开始日期（格式yyyyMMdd）
     * @param endDate 结束日期（格式yyyyMMdd）
     * @param appName 应用名
     * @param executeEnv 执行环境
     * @return 统计结果列表
     */
    List<TestExecutionStats> queryTestExecutionStats(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("appName") String appName,
            @Param("executeEnv") String executeEnv);
    
    /**
     * 获取所有应用名列表（去重）
     * @return 应用名列表
     */
    List<String> findDistinctAppNames();
    
    /**
     * 获取所有执行环境列表（去重）
     * @return 执行环境列表
     */
    List<String> findDistinctExecuteEnvs();
}