package com.example.demo.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ldap.core.LdapTemplate;
import org.springframework.ldap.core.support.LdapContextSource;
import org.springframework.ldap.filter.AndFilter;
import org.springframework.ldap.filter.EqualsFilter;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

@Component
public class CustomLdapAuthenticationProvider implements AuthenticationProvider {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomLdapAuthenticationProvider.class);
    private final LdapTemplate ldapTemplate;
    
    // 允许登录的用户列表
    private static final Set<String> ALLOWED_USERS = new HashSet<>(Arrays.asList(
        "zelai.yang",
        "meng.lv",
        "ji.ma",
        "yixiang.zhang",
        "jack.liu",
        "hongdong.xie",
        "xiaomin.fu",
        "yuanxiang.li"
    ));

    public CustomLdapAuthenticationProvider() {
        LdapContextSource contextSource = new LdapContextSource();
        contextSource.setUrl("ldap://192.168.230.11:389");
        contextSource.setBase("ou=howbuy,dc=howbuy,dc=domain");
        contextSource.setUserDn("<EMAIL>");
        contextSource.setPassword("Howbuy2007");
        try {
            contextSource.afterPropertiesSet();
        } catch (Exception e) {
            logger.error("LDAP context source initialization failed", e);
        }
        this.ldapTemplate = new LdapTemplate(contextSource);
    }

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = authentication.getCredentials().toString();

        // 检查用户是否在允许列表中
        if (!ALLOWED_USERS.contains(username)) {
            logger.error("用户[{}]没有访问权限", username);
            throw new BadCredentialsException("您没有访问系统的权限");
        }

        try {
            AndFilter filter = new AndFilter()
                    .and(new EqualsFilter("sAMAccountName", username));
            
            boolean authenticated = ldapTemplate.authenticate("", filter.toString(), password);
            
            if (authenticated) {
                logger.info("用户[{}]登录成功", username);
                return new UsernamePasswordAuthenticationToken(
                    username, 
                    password,
                    Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
                );
            }
        } catch (Exception e) {
            logger.error("用户[{}]登录失败: {}", username, e.getMessage());
            throw new AuthenticationException("认证失败: " + e.getMessage()) {};
        }
        
        return null;
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return authentication.equals(UsernamePasswordAuthenticationToken.class);
    }
} 