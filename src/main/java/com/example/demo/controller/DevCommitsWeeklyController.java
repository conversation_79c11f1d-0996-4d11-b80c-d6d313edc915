package com.example.demo.controller;

import com.example.demo.entity.primary.DevCommitsWeekly;
import com.example.demo.service.DevCommitsWeeklyService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Controller
@RequestMapping("/commits-weekly")
public class DevCommitsWeeklyController {
    private final DevCommitsWeeklyService devCommitsWeeklyService;

    public DevCommitsWeeklyController(DevCommitsWeeklyService devCommitsWeeklyService) {
        this.devCommitsWeeklyService = devCommitsWeeklyService;
    }

    @GetMapping("/chart")
    public String showChart(
            Model model,
            @RequestParam(required = false) List<String> selectedUsers,
            @RequestParam(required = false) String selectedTeam,
            @RequestParam(required = false) String selectedWeek
    ) {
        // 设置默认团队为CRM
        if (selectedTeam == null || selectedTeam.isEmpty()) {
            selectedTeam = "CRM";
        }

        List<String> weeks = devCommitsWeeklyService.getDistinctWeeks();
        
        // 如果未选择周，默认使用最新的周
        if (selectedWeek == null || selectedWeek.isEmpty() && !weeks.isEmpty()) {
            selectedWeek = weeks.get(0);
        }

        // 获取最近4周的数据
        String endWeek = selectedWeek;
        int weekIndex = weeks.indexOf(selectedWeek);
        String startWeek = weekIndex + 3 < weeks.size() ? weeks.get(weekIndex + 3) : weeks.get(weeks.size() - 1);

        List<String> teams = devCommitsWeeklyService.getDistinctTeams();
        List<DevCommitsWeekly> commits = devCommitsWeeklyService.getCommitsByConditions(
                selectedTeam, selectedUsers, startWeek, endWeek);

        List<String> usernames = selectedTeam != null && !selectedTeam.isEmpty() 
                ? devCommitsWeeklyService.getUsernamesByTeam(selectedTeam)
                : devCommitsWeeklyService.getUsernamesByTeam(null);

        model.addAttribute("commits", commits);
        model.addAttribute("usernames", usernames);
        model.addAttribute("weeks", weeks);
        model.addAttribute("teams", teams);
        model.addAttribute("selectedUsers", selectedUsers);
        model.addAttribute("selectedTeam", selectedTeam);
        model.addAttribute("selectedWeek", selectedWeek);
        model.addAttribute("active", "commits-weekly");
        return "commits-weekly-chart";
    }

    @GetMapping("/users-by-team")
    @ResponseBody
    public List<String> getUsersByTeam(@RequestParam String team) {
        return devCommitsWeeklyService.getUsernamesByTeam(team);
    }
} 