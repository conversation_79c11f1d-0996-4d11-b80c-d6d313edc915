package com.example.demo.controller;

import com.example.demo.entity.primary.DevWorkTimeSpent;
import com.example.demo.service.DevWorkTimeService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Controller
@RequestMapping("/worktime")
public class DevWorkTimeController {
    private final DevWorkTimeService devWorkTimeService;

    public DevWorkTimeController(DevWorkTimeService devWorkTimeService) {
        this.devWorkTimeService = devWorkTimeService;
    }

    @GetMapping("/chart")
    public String showChart(
            Model model,
            @RequestParam(required = false) List<String> selectedUsers,
            @RequestParam(required = false) String selectedTeam,
            @RequestParam(required = false) String startMonth,
            @RequestParam(required = false) String endMonth
    ) {
        // 设置默认团队为CRM
        if (selectedTeam == null || selectedTeam.isEmpty()) {
            selectedTeam = "CRM";
        }

        // 设置默认时间范围为当前月份的前3个月
        if (startMonth == null || endMonth == null) {
            LocalDate now = LocalDate.now();
            // 结束月份为当前月份的上个月
            endMonth = now.minusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
            // 开始月份为结束月份往前3个月
            startMonth = now.minusMonths(4).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        }

        List<String> teams = devWorkTimeService.getDistinctTeams();
        List<DevWorkTimeSpent> workTimes = devWorkTimeService.getWorkTimeByConditions(
                selectedTeam, selectedUsers, startMonth, endMonth);

        List<String> usernames = devWorkTimeService.getDistinctUsers(selectedTeam);

        model.addAttribute("workTimes", workTimes);
        model.addAttribute("usernames", usernames);
        model.addAttribute("teams", teams);
        model.addAttribute("selectedUsers", selectedUsers);
        model.addAttribute("selectedTeam", selectedTeam);
        model.addAttribute("startMonth", startMonth);
        model.addAttribute("endMonth", endMonth);
        model.addAttribute("active", "worktime");
        return "worktime-chart";
    }

    @GetMapping("/users-by-team")
    @ResponseBody
    public List<String> getUsersByTeam(@RequestParam String team) {
        return devWorkTimeService.getDistinctUsers(team);
    }
} 