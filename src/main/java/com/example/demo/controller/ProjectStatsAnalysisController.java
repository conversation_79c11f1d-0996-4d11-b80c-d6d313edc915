package com.example.demo.controller;

import com.example.demo.service.ProjectStatsAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目统计分析控制器
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Controller
@RequestMapping("/project-stats")
public class ProjectStatsAnalysisController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectStatsAnalysisController.class);

    @Autowired
    private ProjectStatsAnalysisService statsService;

    /**
     * 项目统计分析页面
     *
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @param projectNames 项目名称列表（多选）
     * @param model Model对象
     * @return 页面视图名
     */
    @GetMapping("/analysis")
    public String analysisPage(
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd,
            @RequestParam(required = false) List<String> projectNames,
            Model model) {
        
        logger.info("访问项目统计分析页面，参数：灰度日期区间=[{}, {}], 上线日期区间=[{}, {}], 项目名称={}", 
                grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd, projectNames);

        try {
            // 获取所有项目名列表
            List<String> allProjectNames = statsService.findDistinctProjectNames();

            // 如果没有指定项目名，默认选择最近半年内上线的项目
            if (projectNames == null || projectNames.isEmpty()) {
                projectNames = statsService.findRecentProjectsByOnlineDate();
                logger.info("未指定项目名，自动选择最近半年内上线的项目：{}", projectNames);
            }

            // 设置默认日期（如果没有提供的话）
            if (onlineDateStart == null || onlineDateStart.trim().isEmpty()) {
                // 设置为当年第一天
                LocalDate yearStart = LocalDate.now().withDayOfYear(1);
                onlineDateStart = yearStart.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                logger.info("设置默认上线开始日期为当年第一天：{}", onlineDateStart);
            }

            if (onlineDateEnd == null || onlineDateEnd.trim().isEmpty()) {
                // 设置为当前日期
                LocalDate today = LocalDate.now();
                onlineDateEnd = today.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
                logger.info("设置默认上线结束日期为当前日期：{}", onlineDateEnd);
            }

            // 转换日期格式为数据库格式（yyyyMMdd）
            String onlineDateStartDb = convertToDbDateFormat(onlineDateStart);
            String onlineDateEndDb = convertToDbDateFormat(onlineDateEnd);
            String grayDateStartDb = convertToDbDateFormat(grayDateStart);
            String grayDateEndDb = convertToDbDateFormat(grayDateEnd);

            model.addAttribute("allProjectNames", allProjectNames);
            model.addAttribute("selectedProjectNames", projectNames);
            model.addAttribute("selectedGrayDateStart", grayDateStart);
            model.addAttribute("selectedGrayDateEnd", grayDateEnd);
            model.addAttribute("selectedOnlineDateStart", onlineDateStart);
            model.addAttribute("selectedOnlineDateEnd", onlineDateEnd);

            // 将转换后的数据库格式日期也传递给前端（用于调试）
            model.addAttribute("dbGrayDateStart", grayDateStartDb);
            model.addAttribute("dbGrayDateEnd", grayDateEndDb);
            model.addAttribute("dbOnlineDateStart", onlineDateStartDb);
            model.addAttribute("dbOnlineDateEnd", onlineDateEndDb);
            
            // 设置页面激活状态
            model.addAttribute("active", "project-acceptance");
            model.addAttribute("subActive", "project-stats-analysis");

        } catch (Exception e) {
            logger.error("加载项目统计分析页面失败：{}", e.getMessage(), e);
            model.addAttribute("error", "加载页面数据失败：" + e.getMessage());
        }

        return "project-stats-analysis";
    }

    /**
     * 获取项目对比统计数据
     */
    @GetMapping("/api/project-comparison-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getProjectComparisonStats(
            @RequestParam(required = false) List<String> projectNames,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {

        Map<String, Object> result = new HashMap<>();
        try {
            // 转换日期格式为数据库格式
            String grayDateStartDb = convertToDbDateFormat(grayDateStart);
            String grayDateEndDb = convertToDbDateFormat(grayDateEnd);
            String onlineDateStartDb = convertToDbDateFormat(onlineDateStart);
            String onlineDateEndDb = convertToDbDateFormat(onlineDateEnd);

            Map<String, Map<String, Integer>> stats = statsService.getProjectComparisonStats(
                    projectNames, grayDateStartDb, grayDateEndDb, onlineDateStartDb, onlineDateEndDb);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取项目对比统计失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取项目质量指标统计数据
     */
    @GetMapping("/api/project-quality-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getProjectQualityStats(
            @RequestParam(required = false) List<String> projectNames,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {

        Map<String, Object> result = new HashMap<>();
        try {
            // 转换日期格式为数据库格式
            String grayDateStartDb = convertToDbDateFormat(grayDateStart);
            String grayDateEndDb = convertToDbDateFormat(grayDateEnd);
            String onlineDateStartDb = convertToDbDateFormat(onlineDateStart);
            String onlineDateEndDb = convertToDbDateFormat(onlineDateEnd);

            Map<String, Map<String, Object>> stats = statsService.getProjectQualityStats(
                    projectNames, grayDateStartDb, grayDateEndDb, onlineDateStartDb, onlineDateEndDb);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取项目质量指标统计失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取项目开发问题分类对比数据
     */
    @GetMapping("/api/project-dev-issue-comparison")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getProjectDevIssueComparison(
            @RequestParam(required = false) List<String> projectNames,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {

        Map<String, Object> result = new HashMap<>();
        try {
            // 转换日期格式为数据库格式
            String grayDateStartDb = convertToDbDateFormat(grayDateStart);
            String grayDateEndDb = convertToDbDateFormat(grayDateEnd);
            String onlineDateStartDb = convertToDbDateFormat(onlineDateStart);
            String onlineDateEndDb = convertToDbDateFormat(onlineDateEnd);

            Map<String, Map<String, Integer>> stats = statsService.getProjectDevIssueComparison(
                    projectNames, grayDateStartDb, grayDateEndDb, onlineDateStartDb, onlineDateEndDb);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取项目开发问题分类对比失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取项目测试问题分类对比数据
     */
    @GetMapping("/api/project-test-issue-comparison")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getProjectTestIssueComparison(
            @RequestParam(required = false) List<String> projectNames,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {

        Map<String, Object> result = new HashMap<>();
        try {
            // 转换日期格式为数据库格式
            String grayDateStartDb = convertToDbDateFormat(grayDateStart);
            String grayDateEndDb = convertToDbDateFormat(grayDateEnd);
            String onlineDateStartDb = convertToDbDateFormat(onlineDateStart);
            String onlineDateEndDb = convertToDbDateFormat(onlineDateEnd);

            Map<String, Map<String, Integer>> stats = statsService.getProjectTestIssueComparison(
                    projectNames, grayDateStartDb, grayDateEndDb, onlineDateStartDb, onlineDateEndDb);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取项目测试问题分类对比失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 导出项目统计分析数据
     */
    @GetMapping("/api/export-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> exportStats(
            @RequestParam(required = false) List<String> projectNames,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {

        Map<String, Object> result = new HashMap<>();
        try {
            // 转换日期格式为数据库格式
            String grayDateStartDb = convertToDbDateFormat(grayDateStart);
            String grayDateEndDb = convertToDbDateFormat(grayDateEnd);
            String onlineDateStartDb = convertToDbDateFormat(onlineDateStart);
            String onlineDateEndDb = convertToDbDateFormat(onlineDateEnd);

            String filePath = statsService.exportProjectStatsToExcel(
                    projectNames, grayDateStartDb, grayDateEndDb, onlineDateStartDb, onlineDateEndDb);
            result.put("success", true);
            result.put("filePath", filePath);
            result.put("message", "导出成功");
        } catch (Exception e) {
            logger.error("导出项目统计分析数据失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", "导出失败：" + e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 将前端日期格式(yyyy-MM-dd)转换为数据库日期格式(yyyyMMdd)
     *
     * @param dateStr 前端日期字符串
     * @return 数据库日期格式字符串
     */
    private String convertToDbDateFormat(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            // 解析前端日期格式 yyyy-MM-dd
            LocalDate date = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            // 转换为数据库格式 yyyyMMdd
            return date.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        } catch (Exception e) {
            logger.warn("日期格式转换失败：{}", dateStr, e);
            return null;
        }
    }
}
