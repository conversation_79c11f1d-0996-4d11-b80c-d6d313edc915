package com.example.demo.controller;

import com.example.demo.entity.primary.DevCommitsMonthly;
import com.example.demo.service.DevCommitsService;
import org.springframework.web.bind.annotation.*;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import java.util.List;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Controller
@RequestMapping("/commits")
public class DevCommitsController {
    private final DevCommitsService devCommitsService;

    public DevCommitsController(DevCommitsService devCommitsService) {
        this.devCommitsService = devCommitsService;
    }

    @GetMapping("/chart")
    public String showChart(
            Model model,
            @RequestParam(required = false) List<String> selectedUsers,
            @RequestParam(required = false) String selectedTeam,
            @RequestParam(required = false) String startMonth,
            @RequestParam(required = false) String endMonth
    ) {
        if (selectedTeam == null || selectedTeam.isEmpty()) {
            selectedTeam = "CRM";
        }

        if (startMonth == null || endMonth == null) {
            LocalDate now = LocalDate.now();
            endMonth = now.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            startMonth = now.minusYears(1).plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        }

        List<String> teams = devCommitsService.getDistinctTeams();
        List<String> months = devCommitsService.getDistinctMonthly();
        List<String> usernames;
        List<DevCommitsMonthly> commits;

        if (selectedTeam != null && !selectedTeam.isEmpty()) {
            usernames = devCommitsService.getUsernamesByTeam(selectedTeam);
            commits = devCommitsService.getCommitsByTeamAndDateRange(selectedTeam, startMonth, endMonth);
        } else {
            usernames = devCommitsService.getDistinctUsernames();
            if (selectedUsers != null && !selectedUsers.isEmpty()) {
                commits = devCommitsService.getCommitsByUsernames(selectedUsers);
            } else {
                commits = devCommitsService.getAllCommits();
            }
        }

        model.addAttribute("commits", commits);
        model.addAttribute("usernames", usernames);
        model.addAttribute("months", months);
        model.addAttribute("teams", teams);
        model.addAttribute("selectedUsers", selectedUsers);
        model.addAttribute("selectedTeam", selectedTeam);
        model.addAttribute("startMonth", startMonth);
        model.addAttribute("endMonth", endMonth);
        model.addAttribute("active", "commits");
        return "commits-chart";
    }

    @GetMapping("/users-by-team")
    @ResponseBody
    public List<String> getUsersByTeam(@RequestParam String team) {
        return devCommitsService.getUsernamesByTeam(team);
    }
} 