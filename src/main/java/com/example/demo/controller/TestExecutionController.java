package com.example.demo.controller;

import com.example.demo.entity.primary.TestExecutionQueryParam;
import com.example.demo.entity.primary.TestExecutionStats;
import com.example.demo.service.TestExecutionService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 自动化测试执行统计控制器
 * <AUTHOR>
 * @date 2025-03-13 09:56:46
 */
@Controller
@RequestMapping("/test-execution")
public class TestExecutionController {
    private final TestExecutionService testExecutionService;

    public TestExecutionController(TestExecutionService testExecutionService) {
        this.testExecutionService = testExecutionService;
    }

    /**
     * 显示自动化测试执行统计页面
     * @param model 模型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param appName 应用名
     * @param executeEnv 执行环境
     * @return 视图名称
     */
    @GetMapping("/stats")
    public String showStats(
            Model model,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String appName,
            @RequestParam(required = false) String executeEnv
    ) {
        // 获取默认查询参数
        TestExecutionQueryParam queryParam = testExecutionService.getDefaultQueryParam();
        
        // 如果有传入参数，则覆盖默认参数
        if (startDate != null && !startDate.isEmpty()) {
            queryParam.setStartDate(startDate);
        }
        if (endDate != null && !endDate.isEmpty()) {
            queryParam.setEndDate(endDate);
        }
        if (appName != null && !appName.isEmpty()) {
            queryParam.setAppName(appName);
        }
        if (executeEnv != null) {
            queryParam.setExecuteEnv(executeEnv);
        }
        
        // 查询统计数据
        List<TestExecutionStats> stats = testExecutionService.queryTestExecutionStats(queryParam);
        
        // 获取应用名和执行环境列表
        List<String> appNames = testExecutionService.findDistinctAppNames();
        List<String> executeEnvs = testExecutionService.findDistinctExecuteEnvs();
        appNames.add(0, "全部");
        executeEnvs.add(0, "全部");
        // 添加到模型
        model.addAttribute("stats", stats);
        model.addAttribute("appNames", appNames);
        model.addAttribute("executeEnvs", executeEnvs);
        model.addAttribute("queryParam", queryParam);
        model.addAttribute("active", "test-execution");
        
        return "test-execution-stats";
    }
} 