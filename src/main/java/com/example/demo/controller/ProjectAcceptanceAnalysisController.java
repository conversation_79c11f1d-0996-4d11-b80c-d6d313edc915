package com.example.demo.controller;

import com.example.demo.service.ProjectAcceptanceAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目验收数据分析控制器
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Controller
@RequestMapping("/project-acceptance")
public class ProjectAcceptanceAnalysisController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectAcceptanceAnalysisController.class);

    @Autowired
    private ProjectAcceptanceAnalysisService analysisService;

    /**
     * 单项目数据分析页面
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @param model Model对象
     * @return 页面视图名
     */
    @GetMapping("/analysis")
    public String analysisPage(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd,
            Model model) {
        
        logger.info("访问数据分析页面，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);

        try {
            // 获取所有项目名列表
            List<String> projectNames = analysisService.findDistinctProjectNames();
            
            // 如果没有指定项目名，默认选择最新的项目
            if (projectName == null || projectName.trim().isEmpty()) {
                String latestProject = analysisService.findLatestProjectByOnlineDate();
                if (latestProject != null) {
                    projectName = latestProject;
                    logger.info("未指定项目名，自动选择最新项目：{}", projectName);
                }
            }

            model.addAttribute("projectNames", projectNames);
            model.addAttribute("selectedProjectName", projectName);
            model.addAttribute("selectedGrayDateStart", grayDateStart);
            model.addAttribute("selectedGrayDateEnd", grayDateEnd);
            model.addAttribute("selectedOnlineDateStart", onlineDateStart);
            model.addAttribute("selectedOnlineDateEnd", onlineDateEnd);
            
            // 设置页面激活状态
            model.addAttribute("active", "project-acceptance");
            model.addAttribute("subActive", "project-acceptance-analysis");

        } catch (Exception e) {
            logger.error("加载数据分析页面失败：{}", e.getMessage(), e);
            model.addAttribute("error", "加载页面数据失败：" + e.getMessage());
        }

        return "project-acceptance-analysis";
    }

    /**
     * 获取开发问题分类统计数据
     */
    @GetMapping("/api/dev-issue-category-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDevIssueCategoryStats(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Integer> stats = analysisService.getDevIssueCategoryStats(
                    projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取开发问题分类统计失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取测试问题分类统计数据
     */
    @GetMapping("/api/test-issue-category-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getTestIssueCategoryStats(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Integer> stats = analysisService.getTestIssueCategoryStats(
                    projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取测试问题分类统计失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取测试人员Bug数统计数据
     */
    @GetMapping("/api/tester-bug-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getTesterBugStats(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Integer> stats = analysisService.getTesterBugStats(
                    projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取测试人员Bug数统计失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取开发人员Bug数统计数据
     */
    @GetMapping("/api/developer-bug-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDeveloperBugStats(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Integer> stats = analysisService.getDeveloperBugStats(
                    projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取开发人员Bug数统计失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取开发人员问题分类详细统计数据
     */
    @GetMapping("/api/developer-issue-detail-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getDeveloperIssueDetailStats(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Map<String, Integer>> stats = analysisService.getDeveloperIssueDetailStats(
                    projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取开发人员问题分类详细统计失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }

    /**
     * 获取测试人员问题分类详细统计数据
     */
    @GetMapping("/api/tester-issue-detail-stats")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getTesterIssueDetailStats(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String grayDateStart,
            @RequestParam(required = false) String grayDateEnd,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd) {
        
        Map<String, Object> result = new HashMap<>();
        try {
            Map<String, Map<String, Integer>> stats = analysisService.getTesterIssueDetailStats(
                    projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
            result.put("success", true);
            result.put("data", stats);
        } catch (Exception e) {
            logger.error("获取测试人员问题分类详细统计失败：{}", e.getMessage(), e);
            result.put("success", false);
            result.put("message", e.getMessage());
        }
        return ResponseEntity.ok(result);
    }
}
