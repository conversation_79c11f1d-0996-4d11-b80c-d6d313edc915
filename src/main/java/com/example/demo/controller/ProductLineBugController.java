package com.example.demo.controller;

import com.example.demo.service.ProductLineBugService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

@Controller
@RequestMapping("/product-line")
public class ProductLineBugController {
    private final ProductLineBugService productLineBugService;

    public ProductLineBugController(ProductLineBugService productLineBugService) {
        this.productLineBugService = productLineBugService;
    }

    @GetMapping("/bugs")
    public String showBugStatistics(
            Model model,
            @RequestParam(required = false) String startMonth,
            @RequestParam(required = false) String endMonth,
            @RequestParam(required = false) String selectedCategory
    ) {
        // 设置默认时间范围为最近12个月
        if (startMonth == null || endMonth == null) {
            LocalDate now = LocalDate.now();
            endMonth = now.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            startMonth = now.minusMonths(11).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        }

        Map<String, Map<String, Integer>> statistics = productLineBugService.getBugStatistics(startMonth, endMonth);
        List<String> categories = productLineBugService.getAllCategories();
        
        model.addAttribute("statistics", statistics);
        model.addAttribute("categories", categories);
        model.addAttribute("startMonth", startMonth);
        model.addAttribute("endMonth", endMonth);
        model.addAttribute("selectedCategory", selectedCategory);
        model.addAttribute("active", "product-line");
        return "product-line-bugs";
    }
} 