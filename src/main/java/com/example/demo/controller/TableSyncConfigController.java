package com.example.demo.controller;

import com.example.demo.entity.secondary.TableSyncConfig;
import com.example.demo.service.TableSyncConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 表同步配置控制器
 *
 * <AUTHOR>
 * @date 2024-05-30 07:52:11
 */
@Controller
@RequestMapping("/table-sync")
public class TableSyncConfigController {

    @Autowired
    private TableSyncConfigService tableSyncConfigService;

    /**
     * 表同步配置查询页面
     *
     * @param sourceDb 源库名称
     * @param sourceTable 源表名称
     * @param targetTable 目标表名称
     * @param syncType 接入方式
     * @param dsAdmin 管理员模式（通过URL参数ds-admin=true开启）
     * @param model Model对象
     * @return 页面视图名
     */
    @GetMapping("/configs")
    public String tableSyncConfigs(
            @RequestParam(required = false) String sourceDb,
            @RequestParam(required = false) String sourceTable,
            @RequestParam(required = false) String targetTable,
            @RequestParam(required = false) String syncType,
            @RequestParam(name = "ds-admin", required = false) String dsAdmin,
            Model model) {
        
        // 构建查询参数对象
        QueryParam queryParam = new QueryParam();
        queryParam.setSourceDb(sourceDb);
        queryParam.setSourceTable(sourceTable);
        queryParam.setTargetTable(targetTable);
        queryParam.setSyncType(syncType);
        
        // 查询有效的表同步配置
        List<TableSyncConfig> configs = tableSyncConfigService.queryValidConfigs(
                sourceDb, sourceTable, targetTable, syncType);
        
        // 获取源库名称下拉框选项
        List<String> sourceDbs = tableSyncConfigService.getSourceDbs();
        
        // 获取接入方式下拉框选项
        List<String> syncTypes = tableSyncConfigService.getSyncTypes();
        
        // 设置模型数据
        model.addAttribute("configs", configs);
        model.addAttribute("sourceDbs", sourceDbs);
        model.addAttribute("syncTypes", syncTypes);
        model.addAttribute("queryParam", queryParam);
        model.addAttribute("publicActive", "table-sync");
        
        // 设置管理员模式标志
        boolean isAdmin = "true".equals(dsAdmin);
        model.addAttribute("isAdmin", isAdmin);
        
        return "table-sync-config";
    }
    
    /**
     * 保存表同步配置（新增或更新）
     *
     * @param config 表同步配置信息
     * @return 操作结果
     */
    @PostMapping("/config/save")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> saveConfig(@RequestBody TableSyncConfig config) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 根据ID判断是新增还是更新
            if (config.getId() == null) {
                // 新增配置
                tableSyncConfigService.addConfig(config);
                result.put("message", "新增配置成功");
            } else {
                // 更新配置
                tableSyncConfigService.updateConfig(config);
                result.put("message", "更新配置成功");
            }
            result.put("success", true);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "操作失败：" + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 根据ID获取表同步配置
     *
     * @param id 配置ID
     * @return 配置信息
     */
    @GetMapping("/config/{id}")
    @ResponseBody
    public ResponseEntity<TableSyncConfig> getConfig(@PathVariable Long id) {
        TableSyncConfig config = tableSyncConfigService.getConfigById(id);
        return ResponseEntity.ok(config);
    }
    
    /**
     * 逻辑删除表同步配置
     *
     * @param id 配置ID
     * @return 操作结果
     */
    @PostMapping("/config/{id}/delete")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> deleteConfig(@PathVariable Long id) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            boolean success = tableSyncConfigService.logicalDeleteConfig(id);
            if (success) {
                result.put("success", true);
                result.put("message", "删除配置成功");
            } else {
                result.put("success", false);
                result.put("message", "删除配置失败：配置不存在或已被删除");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除配置失败：" + e.getMessage());
        }
        
        return ResponseEntity.ok(result);
    }
    
    /**
     * 查询参数类
     */
    public static class QueryParam {
        private String sourceDb;
        private String sourceTable;
        private String targetTable;
        private String syncType;
        
        public String getSourceDb() {
            return sourceDb;
        }
        
        public void setSourceDb(String sourceDb) {
            this.sourceDb = sourceDb;
        }
        
        public String getSourceTable() {
            return sourceTable;
        }
        
        public void setSourceTable(String sourceTable) {
            this.sourceTable = sourceTable;
        }
        
        public String getTargetTable() {
            return targetTable;
        }
        
        public void setTargetTable(String targetTable) {
            this.targetTable = targetTable;
        }
        
        public String getSyncType() {
            return syncType;
        }
        
        public void setSyncType(String syncType) {
            this.syncType = syncType;
        }
    }
} 