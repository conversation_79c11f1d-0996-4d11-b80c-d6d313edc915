package com.example.demo.controller;

import com.example.demo.dto.CommitStatsRequestDTO;
import com.example.demo.dto.CommitStatsResponseDTO;
import com.example.demo.service.CommitStatsService;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 代码提交统计控制器
 * <AUTHOR>
 * @date 2025-08-20 16:01:37
 */
@Controller
@RequestMapping("/commit-stats")
public class CommitStatsController {

    private final CommitStatsService commitStatsService;

    public CommitStatsController(CommitStatsService commitStatsService) {
        this.commitStatsService = commitStatsService;
    }

    /**
     * 统计报表页面
     */
    @GetMapping
    public String index(Model model) {
        try {
            model.addAttribute("availableYears", commitStatsService.getAvailableYears());
            model.addAttribute("allTeams", commitStatsService.getAllTeams());
            return "commit-stats/index";
        } catch (Exception e) {
            model.addAttribute("error", "页面加载失败：" + e.getMessage());
            return "error/500";
        }
    }

    /**
     * 获取统计数据
     */
    @PostMapping("/data")
    @ResponseBody
    public ResponseEntity<List<CommitStatsResponseDTO>> getStatsData(@RequestBody CommitStatsRequestDTO request) {
        try {
            List<CommitStatsResponseDTO> data = commitStatsService.getStatsData(request);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 导出统计数据
     */
    @PostMapping("/export")
    public void exportData(@RequestBody CommitStatsRequestDTO request, HttpServletResponse response) {
        try {
            commitStatsService.exportStatsData(request, response);
        } catch (Exception e) {
            throw new RuntimeException("导出失败：" + e.getMessage(), e);
        }
    }
}