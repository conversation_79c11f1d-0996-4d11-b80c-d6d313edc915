package com.example.demo.controller;

import com.example.demo.entity.primary.DevBugsOfCommits;
import com.example.demo.service.DevBugsService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

@Controller
@RequestMapping("/bugs")
public class DevBugsController {
    private final DevBugsService devBugsService;

    public DevBugsController(DevBugsService devBugsService) {
        this.devBugsService = devBugsService;
    }

    @GetMapping("/chart")
    public String showChart(
            Model model,
            @RequestParam(required = false) List<String> selectedUsers,
            @RequestParam(required = false) String selectedTeam,
            @RequestParam(required = false) String startMonth,
            @RequestParam(required = false) String endMonth,
            @RequestParam(required = false) String selectedMode
    ) {
        // 设置默认团队为CRM
        if (selectedTeam == null || selectedTeam.isEmpty()) {
            selectedTeam = "CRM";
        }

        // 设置默认周期为半年
        if (selectedMode == null || selectedMode.isEmpty()) {
            selectedMode = "half_year";
        }

        // 如果未指定时间范围，设置默认为最近一年
        if (startMonth == null || endMonth == null) {
            LocalDate now = LocalDate.now();
            endMonth = now.format(DateTimeFormatter.ofPattern("yyyy-MM"));
            startMonth = now.minusYears(1).plusMonths(1).format(DateTimeFormatter.ofPattern("yyyy-MM"));
        }

        List<String> teams = devBugsService.getDistinctTeams();
        List<String> months = devBugsService.getDistinctMonths();
        List<DevBugsOfCommits> bugs = devBugsService.getBugsByConditions(
                selectedTeam, selectedUsers, startMonth+"01", endMonth+"01", selectedMode);

        List<String> usernames = selectedTeam != null && !selectedTeam.isEmpty() 
                ? devBugsService.getUsernamesByTeam(selectedTeam)
                : devBugsService.getUsernamesByTeam(null);

        model.addAttribute("bugs", bugs);
        model.addAttribute("usernames", usernames);
        model.addAttribute("months", months);
        model.addAttribute("teams", teams);
        model.addAttribute("selectedUsers", selectedUsers);
        model.addAttribute("selectedTeam", selectedTeam);
        model.addAttribute("startMonth", startMonth);
        model.addAttribute("endMonth", endMonth);
        model.addAttribute("selectedMode", selectedMode);
        model.addAttribute("active", "bugs");
        return "bugs-chart";
    }

    @GetMapping("/users-by-team")
    @ResponseBody
    public List<String> getUsersByTeam(@RequestParam String team) {
        return devBugsService.getUsernamesByTeam(team);
    }
} 