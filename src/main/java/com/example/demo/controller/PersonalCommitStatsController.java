package com.example.demo.controller;

import com.example.demo.dto.PersonalCommitStatsRequestDTO;
import com.example.demo.dto.PersonalCommitStatsResponseDTO;
import com.example.demo.service.PersonalCommitStatsService;
import com.example.demo.service.CommitStatsService;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 个人代码提交统计控制器
 * <AUTHOR>
 * @date 2025-08-23 10:15:55
 */
@Controller
@RequestMapping("/personal-commit-stats")
public class PersonalCommitStatsController {

    private final PersonalCommitStatsService personalCommitStatsService;
    private final CommitStatsService commitStatsService;

    public PersonalCommitStatsController(PersonalCommitStatsService personalCommitStatsService,
                                        CommitStatsService commitStatsService) {
        this.personalCommitStatsService = personalCommitStatsService;
        this.commitStatsService = commitStatsService;
    }

    /**
     * 个人统计报表页面
     */
    @GetMapping
    public String index(Model model) {
        try {
            model.addAttribute("active", "personal-commit-stats");
            model.addAttribute("availableYears", commitStatsService.getAvailableYears());
            model.addAttribute("allTeams", commitStatsService.getAllTeams());
            model.addAttribute("allDevelopers", personalCommitStatsService.getAllDevelopers());
            return "personal-commit-stats/index";
        } catch (Exception e) {
            model.addAttribute("error", "页面加载失败：" + e.getMessage());
            return "error/500";
        }
    }

    /**
     * 获取个人统计数据
     */
    @PostMapping("/data")
    @ResponseBody
    public ResponseEntity<List<PersonalCommitStatsResponseDTO>> getPersonalStatsData(@RequestBody PersonalCommitStatsRequestDTO request) {
        try {
            List<PersonalCommitStatsResponseDTO> data = personalCommitStatsService.getPersonalStatsData(request);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 根据团队获取开发者列表
     */
    @PostMapping("/developers")
    @ResponseBody
    public ResponseEntity<List<String>> getDevelopersByTeams(@RequestBody List<String> teams) {
        try {
            List<String> developers;
            if (teams == null || teams.isEmpty()) {
                developers = personalCommitStatsService.getAllDevelopers();
            } else {
                developers = personalCommitStatsService.getDevelopersByTeams(teams);
            }
            return ResponseEntity.ok(developers);
        } catch (Exception e) {
            return ResponseEntity.internalServerError().build();
        }
    }
}