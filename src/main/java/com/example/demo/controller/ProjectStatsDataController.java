package com.example.demo.controller;

import com.example.demo.dto.ProjectStatsDataDto;
import com.example.demo.service.ProjectStatsDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目统计数据控制器
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Controller
@RequestMapping("/project-stats-data")
public class ProjectStatsDataController {

    private static final Logger logger = LoggerFactory.getLogger(ProjectStatsDataController.class);

    @Autowired
    private ProjectStatsDataService projectStatsDataService;

    /**
     * 项目统计数据页面
     *
     * @param projectName 项目名称
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @param model Model对象
     * @return 页面视图名
     */
    @GetMapping("/list")
    public String listPage(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String onlineDateStart,
            @RequestParam(required = false) String onlineDateEnd,
            Model model) {
        
        logger.info("访问项目统计数据页面，参数：项目名={}, 上线日期区间=[{}, {}]", 
                projectName, onlineDateStart, onlineDateEnd);

        try {
            // 设置默认查询条件：当年1月1日至当前日期
            if (!StringUtils.hasText(onlineDateStart)) {
                onlineDateStart = LocalDate.now().getYear() + "0101";
            } else {
                // 将前端的yyyy-MM-dd格式转换为yyyyMMdd格式
                onlineDateStart = onlineDateStart.replace("-", "");
            }

            if (!StringUtils.hasText(onlineDateEnd)) {
                onlineDateEnd = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            } else {
                // 将前端的yyyy-MM-dd格式转换为yyyyMMdd格式
                onlineDateEnd = onlineDateEnd.replace("-", "");
            }

            // 查询项目统计数据
            List<ProjectStatsDataDto> projectStatsDataList = projectStatsDataService.getProjectStatsDataList(
                    projectName, onlineDateStart, onlineDateEnd);

            // 设置页面数据
            model.addAttribute("projectStatsDataList", projectStatsDataList);
            model.addAttribute("projectName", projectName);
            model.addAttribute("onlineDateStart", onlineDateStart);
            model.addAttribute("onlineDateEnd", onlineDateEnd);

            // 格式化日期用于前端显示
            model.addAttribute("onlineDateStartFormatted", formatDateForInput(onlineDateStart));
            model.addAttribute("onlineDateEndFormatted", formatDateForInput(onlineDateEnd));

            model.addAttribute("active", "project-stats-data");

            logger.info("项目统计数据页面数据加载完成，共{}条记录", projectStatsDataList.size());

        } catch (Exception e) {
            logger.error("项目统计数据页面加载失败", e);
            model.addAttribute("error", "数据加载失败：" + e.getMessage());
        }

        return "project-stats-data-list";
    }

    /**
     * 获取项目分析报告内容
     *
     * @param projectName 项目名称
     * @return 项目分析报告内容
     */
    @GetMapping("/analysis-content")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> getAnalysisContent(@RequestParam String projectName) {
        logger.info("获取项目分析报告内容，项目名：{}", projectName);

        Map<String, Object> result = new HashMap<>();

        try {
            if (!StringUtils.hasText(projectName)) {
                result.put("success", false);
                result.put("message", "项目名称不能为空");
                return ResponseEntity.badRequest().body(result);
            }

            String content = projectStatsDataService.getProjectAnalysisContent(projectName);

            if (content != null) {
                result.put("success", true);
                result.put("content", content);
                result.put("projectName", projectName);
            } else {
                result.put("success", false);
                result.put("message", "未找到该项目的分析报告");
            }

        } catch (Exception e) {
            logger.error("获取项目分析报告内容失败，项目名：{}", projectName, e);
            result.put("success", false);
            result.put("message", "获取分析报告失败：" + e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * 项目分析报告页面
     *
     * @param projectName 项目名称
     * @param model Model对象
     * @return 页面视图名
     */
    @GetMapping("/analysis-report")
    public String analysisReportPage(@RequestParam String projectName, Model model) {
        logger.info("访问项目分析报告页面，项目名：{}", projectName);

        try {
            if (!StringUtils.hasText(projectName)) {
                model.addAttribute("error", "项目名称不能为空");
                return "project-analysis-report";
            }

            String content = projectStatsDataService.getProjectAnalysisContent(projectName);

            if (content != null) {
                model.addAttribute("projectName", projectName);
                model.addAttribute("analysisContent", content);
                model.addAttribute("hasContent", true);
            } else {
                model.addAttribute("projectName", projectName);
                model.addAttribute("hasContent", false);
                model.addAttribute("message", "该项目暂无分析报告内容");
            }

        } catch (Exception e) {
            logger.error("项目分析报告页面加载失败，项目名：{}", projectName, e);
            model.addAttribute("error", "报告加载失败：" + e.getMessage());
            model.addAttribute("projectName", projectName);
        }

        return "project-analysis-report";
    }

    /**
     * 格式化日期用于前端输入框显示
     *
     * @param dateStr yyyyMMdd格式的日期字符串
     * @return yyyy-MM-dd格式的日期字符串
     */
    private String formatDateForInput(String dateStr) {
        if (!StringUtils.hasText(dateStr) || dateStr.length() != 8) {
            return "";
        }
        try {
            return dateStr.substring(0, 4) + "-" + dateStr.substring(4, 6) + "-" + dateStr.substring(6, 8);
        } catch (Exception e) {
            logger.warn("日期格式转换失败：{}", dateStr, e);
            return "";
        }
    }
}
