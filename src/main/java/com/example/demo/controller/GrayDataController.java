package com.example.demo.controller;

import com.example.demo.entity.primary.GrayData;
import com.example.demo.exception.ProjectDuplicateException;
import com.example.demo.service.GrayDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 灰度数据控制器
 *
 * <AUTHOR>
 * @date 2025-07-09 16:54:24
 */
@Controller
@RequestMapping("/gray-data")
public class GrayDataController {

    private static final Logger logger = LoggerFactory.getLogger(GrayDataController.class);

    @Autowired
    private GrayDataService grayDataService;
    
    /**
     * 项目验收数据页面
     *
     * @param projectName 项目名（支持模糊查询）
     * @param acceptanceEnv 验收环境
     * @param model Model对象
     * @return 页面视图名
     */
    @GetMapping("/list")
    public String grayDataList(
            @RequestParam(required = false) String projectName,
            @RequestParam(required = false) String acceptanceEnv,
            Model model) {
        
        // 查询数据
        List<GrayData> grayDataList = grayDataService.findByConditions(projectName, acceptanceEnv);
        
        // 获取下拉框数据
        List<String> projectNames = grayDataService.findDistinctProjectNames();
        List<String> acceptanceEnvs = grayDataService.findDistinctAcceptanceEnvs();
        
        model.addAttribute("grayDataList", grayDataList);
        model.addAttribute("projectNames", projectNames);
        model.addAttribute("acceptanceEnvs", acceptanceEnvs);
        model.addAttribute("selectedProjectName", projectName);
        model.addAttribute("selectedAcceptanceEnv", acceptanceEnv);
        // 设置页面激活状态
        model.addAttribute("active", "project-acceptance");
        model.addAttribute("subActive", "gray-data-detail");
        
        return "gray-data-list";
    }
    
    /**
     * 获取项目名列表（用于下拉框提示）
     *
     * @return 项目名列表
     */
    @GetMapping("/project-names")
    @ResponseBody
    public List<String> getProjectNames() {
        return grayDataService.findDistinctProjectNames();
    }

    /**
     * Excel文件上传导入
     *
     * @param file Excel文件
     * @param grayDate 灰度日期
     * @param onlineDate 上线日期
     * @return 导入结果
     */
    @PostMapping("/import")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> importExcel(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "grayDate", required = false) String grayDate,
            @RequestParam(value = "onlineDate", required = false) String onlineDate) {
        logger.info("收到Excel文件上传请求，文件名: {}, 灰度日期: {}, 上线日期: {}",
                file != null ? file.getOriginalFilename() : "null", grayDate, onlineDate);
        Map<String, Object> result = new HashMap<>();

        try {
            String message = grayDataService.importExcel(file, grayDate, onlineDate);
            logger.info("Excel文件导入成功: {}", message);
            result.put("success", true);
            result.put("message", message);
        } catch (ProjectDuplicateException e) {
            logger.warn("项目重复导入: {}", e.getMessage());
            result.put("success", false);
            result.put("duplicate", true);
            result.put("projectName", e.getProjectName());
            result.put("existingRecordCount", e.getExistingRecordCount());
            result.put("message", e.getMessage());
        } catch (Exception e) {
            logger.error("Excel文件导入失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("duplicate", false);
            result.put("message", e.getMessage());
        }

        return ResponseEntity.ok(result);
    }

    /**
     * Excel文件上传导入（支持覆盖）
     *
     * @param file Excel文件
     * @param forceOverwrite 是否强制覆盖
     * @param grayDate 灰度日期
     * @param onlineDate 上线日期
     * @return 导入结果
     */
    @PostMapping("/import-with-overwrite")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> importExcelWithOverwrite(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "forceOverwrite", defaultValue = "false") boolean forceOverwrite,
            @RequestParam(value = "grayDate", required = false) String grayDate,
            @RequestParam(value = "onlineDate", required = false) String onlineDate) {
        logger.info("收到Excel文件覆盖导入请求，文件名: {}, 强制覆盖: {}, 灰度日期: {}, 上线日期: {}",
                file != null ? file.getOriginalFilename() : "null", forceOverwrite, grayDate, onlineDate);
        Map<String, Object> result = new HashMap<>();

        try {
            String message = grayDataService.importExcel(file, forceOverwrite, grayDate, onlineDate);
            logger.info("Excel文件导入成功: {}", message);
            result.put("success", true);
            result.put("message", message);
        } catch (ProjectDuplicateException e) {
            logger.warn("项目重复导入: {}", e.getMessage());
            result.put("success", false);
            result.put("duplicate", true);
            result.put("projectName", e.getProjectName());
            result.put("existingRecordCount", e.getExistingRecordCount());
            result.put("message", e.getMessage());
        } catch (Exception e) {
            logger.error("Excel文件导入失败: {}", e.getMessage(), e);
            result.put("success", false);
            result.put("duplicate", false);
            result.put("message", e.getMessage());
        }

        return ResponseEntity.ok(result);
    }
}
