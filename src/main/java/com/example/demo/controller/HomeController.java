package com.example.demo.controller;

import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

@Controller
public class HomeController {
    
    @GetMapping("/")
    public String home() {
        // 检查用户是否已登录
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        boolean isAuthenticated = authentication != null && 
                                 authentication.isAuthenticated() && 
                                 !authentication.getName().equals("anonymousUser");
        
        // 如果已登录，重定向到需要登录的页面
        if (isAuthenticated) {
            return "redirect:/commits/chart";
        }
        
        // 未登录，重定向到公共页面
        return "redirect:/table-sync/configs";
    }
} 