package com.example.demo.controller;

import com.example.demo.entity.primary.DevWorkTimeSpentStats;
import com.example.demo.service.DevWorkTimeSpentStatsService;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@Controller
@RequestMapping("/worktime-stats")
public class DevWorkTimeSpentStatsController {
    private final DevWorkTimeSpentStatsService workTimeSpentStatsService;

    public DevWorkTimeSpentStatsController(DevWorkTimeSpentStatsService workTimeSpentStatsService) {
        this.workTimeSpentStatsService = workTimeSpentStatsService;
    }

    @GetMapping("/chart")
    public String showChart(
            Model model,
            @RequestParam(required = false) String selectedTeam
    ) {
        if (selectedTeam == null || selectedTeam.isEmpty()) {
            selectedTeam = "CRM";
        }

        List<DevWorkTimeSpentStats> stats = workTimeSpentStatsService.getWorkTimeStats();
        List<String> teams = workTimeSpentStatsService.getDistinctTeams();
        
        model.addAttribute("stats", stats);
        model.addAttribute("teams", teams);
        model.addAttribute("selectedTeam", selectedTeam);
        model.addAttribute("active", "worktime-stats");
        return "worktime-stats-chart";
    }
} 