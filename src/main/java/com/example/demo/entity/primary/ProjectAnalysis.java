package com.example.demo.entity.primary;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 项目分析实体类
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Getter
@Setter
public class ProjectAnalysis {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 分析内容(MD格式文档)
     */
    private String analysisContent;

    /**
     * 项目上线日期yyyyMMdd
     */
    private String onlineDate;

    /**
     * 项目灰度日期yyyyMMdd
     */
    private String grayDate;

    /**
     * 创建时间戳
     */
    private Date createTime;

    /**
     * 更新时间戳
     */
    private Date updateTime;
}
