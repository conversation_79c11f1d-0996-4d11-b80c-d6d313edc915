package com.example.demo.entity.primary;

import java.util.Date;

public class TestTestsetResult {
    private Long id;

    private Date 执行时间;

    private String 运行批id;

    private Integer 测试集id;

    private String 测试集名称;

    private String 应用名;

    private String 测试案例版本;

    private String 执行环境;

    private String 接口通过率;

    private String 案例通过率;

    /**
    * 单位是分钟
    */
    private Double 耗时;

    /**
    * 如果要统计耗时，建议筛选这个值为1的记录。这个值为1，说明实际执行过的案例数与应当要执行的偏差小
    */
    private Byte 是否偏差度小;

    private Date createDate;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date get执行时间() {
        return 执行时间;
    }

    public void set执行时间(Date 执行时间) {
        this.执行时间 = 执行时间;
    }

    public String get运行批id() {
        return 运行批id;
    }

    public void set运行批id(String 运行批id) {
        this.运行批id = 运行批id;
    }

    public Integer get测试集id() {
        return 测试集id;
    }

    public void set测试集id(Integer 测试集id) {
        this.测试集id = 测试集id;
    }

    public String get测试集名称() {
        return 测试集名称;
    }

    public void set测试集名称(String 测试集名称) {
        this.测试集名称 = 测试集名称;
    }

    public String get应用名() {
        return 应用名;
    }

    public void set应用名(String 应用名) {
        this.应用名 = 应用名;
    }

    public String get测试案例版本() {
        return 测试案例版本;
    }

    public void set测试案例版本(String 测试案例版本) {
        this.测试案例版本 = 测试案例版本;
    }

    public String get执行环境() {
        return 执行环境;
    }

    public void set执行环境(String 执行环境) {
        this.执行环境 = 执行环境;
    }

    public String get接口通过率() {
        return 接口通过率;
    }

    public void set接口通过率(String 接口通过率) {
        this.接口通过率 = 接口通过率;
    }

    public String get案例通过率() {
        return 案例通过率;
    }

    public void set案例通过率(String 案例通过率) {
        this.案例通过率 = 案例通过率;
    }

    public Double get耗时() {
        return 耗时;
    }

    public void set耗时(Double 耗时) {
        this.耗时 = 耗时;
    }

    public Byte get是否偏差度小() {
        return 是否偏差度小;
    }

    public void set是否偏差度小(Byte 是否偏差度小) {
        this.是否偏差度小 = 是否偏差度小;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }
}