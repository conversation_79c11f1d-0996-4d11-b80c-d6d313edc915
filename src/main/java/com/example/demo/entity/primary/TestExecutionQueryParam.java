package com.example.demo.entity.primary;

import lombok.Getter;
import lombok.Setter;

/**
 * 自动化测试执行查询参数DTO
 * <AUTHOR>
 * @date 2025-03-13 09:56:46
 */
@Getter
@Setter
public class TestExecutionQueryParam {
    /**
     * 开始日期（格式yyyyMMdd）
     */
    private String startDate;
    
    /**
     * 结束日期（格式yyyyMMdd）
     */
    private String endDate;
    
    /**
     * 应用名
     */
    private String appName;
    
    /**
     * 执行环境
     */
    private String executeEnv;
} 