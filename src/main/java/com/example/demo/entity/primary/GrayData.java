package com.example.demo.entity.primary;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 灰度数据实体类
 *
 * <AUTHOR>
 * @date 2025-07-09 16:54:24
 */
@Getter
@Setter
public class GrayData {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 项目名
     */
    private String projectName;

    /**
     * 验收环境
     */
    private String acceptanceEnv;

    /**
     * 平台
     */
    private String platform;

    /**
     * 问题类型
     */
    private String issueType;

    /**
     * 模块
     */
    private String module;

    /**
     * 页面
     */
    private String page;

    /**
     * 问题描述
     */
    private String issueDescription;

    /**
     * 修改进度
     */
    private String modifyProgress;

    /**
     * 当前是否可验
     */
    private String currentTestable;

    /**
     * 验收人
     */
    private String acceptor;

    /**
     * 问题归因
     */
    private String issueAttribution;

    /**
     * 开发对接人
     */
    private String devContact;

    /**
     * 开发备注
     */
    private String devRemark;

    /**
     * 开发问题分类
     */
    private String devIssueCategory;

    /**
     * 是否有测试用例
     */
    private String hasTestCase;

    /**
     * 测试问题分类
     */
    private String testIssueCategory;

    /**
     * 测试原因分析
     */
    private String testReasonAnalysis;

    /**
     * 测试人员
     */
    private String tester;

    /**
     * 灰度日期
     */
    private String grayDate;

    /**
     * 上线日期
     */
    private String onlineDate;

    /**
     * 创建时间戳
     */
    private Date createTime;

    /**
     * 更新时间戳
     */
    private Date updateTime;
}
