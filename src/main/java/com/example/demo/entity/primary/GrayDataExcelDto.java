package com.example.demo.entity.primary;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Getter;
import lombok.Setter;

/**
 * 灰度数据Excel导入DTO
 *
 * <AUTHOR>
 * @date 2025-07-09 16:54:24
 */
@Getter
@Setter
public class GrayDataExcelDto {
    
    @ExcelProperty("验收环境")
    private String acceptanceEnv;

    @ExcelProperty("平台")
    private String platform;

    @ExcelProperty("问题类型")
    private String issueType;

    @ExcelProperty("模块")
    private String module;

    @ExcelProperty("页面")
    private String page;

    @ExcelProperty("问题描述")
    private String issueDescription;

    @ExcelProperty("修改进度")
    private String modifyProgress;

    @ExcelProperty("当前是否可验")
    private String currentTestable;

    @ExcelProperty("验收人")
    private String acceptor;

    @ExcelProperty("问题归因")
    private String issueAttribution;

    @ExcelProperty("开发对接人")
    private String devContact;

    @ExcelProperty("开发备注")
    private String devRemark;

    @ExcelProperty("开发问题分类")
    private String devIssueCategory;

    @ExcelProperty("是否有测试用例")
    private String hasTestCase;

    @ExcelProperty("测试问题分类")
    private String testIssueCategory;

    @ExcelProperty("测试原因分析")
    private String testReasonAnalysis;

    @ExcelProperty("测试人员")
    private String tester;
}
