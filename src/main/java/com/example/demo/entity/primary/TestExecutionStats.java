package com.example.demo.entity.primary;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 自动化测试执行统计结果DTO
 * <AUTHOR>
 * @date 2025-03-13 09:56:46
 */
@Getter
@Setter
public class TestExecutionStats {
    /**
     * 执行日期（格式yyyyMMdd）
     */
    private String executeDate;
    
    /**
     * 应用名
     */
    private String appName;
    
    /**
     * 测试集耗时加总（分钟）
     */
    private Double totalDuration;
    
    /**
     * 案例平均通过率
     */
    private String avgPassRate;
    
    /**
     * 开始时间
     */
    private Date startTime;
    
    /**
     * 结束时间
     */
    private Date endTime;
    
    /**
     * 执行环境
     */
    private String executeEnv;
} 