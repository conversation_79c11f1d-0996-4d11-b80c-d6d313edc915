package com.example.demo.entity.secondary;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 表同步配置实体类
 *
 * <AUTHOR>
 * @date 2024-05-30 07:44:32
 */
@Getter
@Setter
public class TableSyncConfig {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 源库名称
     */
    private String sourceDb;

    /**
     * 源表名称
     */
    private String sourceTable;

    /**
     * 目标表名称
     */
    private String targetTable;

    /**
     * 接入方式
     */
    private String syncType;

    /**
     * 状态：0-无效，1-有效
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
} 