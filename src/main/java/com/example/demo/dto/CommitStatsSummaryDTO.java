package com.example.demo.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 统计汇总数据
 * <AUTHOR>
 * @date 2025-08-20 16:01:37
 */
@Getter
@Setter
public class CommitStatsSummaryDTO {
    
    /**
     * 时间周期
     */
    private String period;
    
    /**
     * 总代码行数
     */
    private Long totalLines;
    
    /**
     * 开发人员数量
     */
    private Integer developerCount;
    
    /**
     * 团队数量
     */
    private Integer teamCount;
    
    /**
     * 人均代码行数
     */
    private Double avgLinesPerDeveloper;
}