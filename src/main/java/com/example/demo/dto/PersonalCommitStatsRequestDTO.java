package com.example.demo.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 个人代码提交统计请求参数
 * <AUTHOR>
 * @date 2025-08-23 10:15:55
 */
@Getter
@Setter
public class PersonalCommitStatsRequestDTO {
    
    /**
     * 年份列表
     */
    private List<Integer> years;
    
    /**
     * 团队列表
     */
    private List<String> teams;
    
    /**
     * 人员用户名列表
     */
    private List<String> usernames;
    
    /**
     * 统计维度：YEAR/QUARTER/HALF_YEAR/TOP_RANKING
     */
    private String dimensionType;
    
    /**
     * 排名数量限制（用于Top N排名）
     */
    private Integer topLimit;
    
    /**
     * 排名方式：TOTAL_LINES/AVG_LINES/GROWTH_RATE
     */
    private String rankingType;
}