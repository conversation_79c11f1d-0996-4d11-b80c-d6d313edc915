package com.example.demo.dto;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 代码提交统计请求参数
 * <AUTHOR>
 * @date 2025-08-20 16:01:37
 */
@Getter
@Setter
public class CommitStatsRequestDTO {
    
    /**
     * 年份列表
     */
    private List<Integer> years;
    
    /**
     * 团队列表
     */
    private List<String> teams;
    
    /**
     * 统计维度：YEAR/QUARTER/TEAM_YEAR/TEAM_QUARTER
     */
    private String dimensionType;
}