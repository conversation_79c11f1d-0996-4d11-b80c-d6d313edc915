package com.example.demo.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 个人代码提交统计响应数据
 * <AUTHOR>
 * @date 2025-08-23 10:15:55
 */
@Getter
@Setter
public class PersonalCommitStatsResponseDTO {
    
    /**
     * 时间周期
     */
    private String period;
    
    /**
     * 用户名
     */
    private String username;
    
    /**
     * 团队名称
     */
    private String team;
    
    /**
     * 代码行数
     */
    private Long totalLines;
    
    /**
     * 平均每月代码行数
     */
    private Long avgMonthlyLines;
    
    /**
     * 增长率
     */
    private Double growthRate;
    
    /**
     * 在团队中的排名
     */
    private Integer teamRanking;
    
    /**
     * 在团队中的占比
     */
    private Double teamPercentage;
    
    /**
     * 全公司排名
     */
    private Integer globalRanking;
    
    /**
     * 是否为预估增长率
     */
    private Boolean isEstimated;
    
    /**
     * 同比增长率（与去年同期对比）
     */
    private Double yearOverYearGrowthRate;
}