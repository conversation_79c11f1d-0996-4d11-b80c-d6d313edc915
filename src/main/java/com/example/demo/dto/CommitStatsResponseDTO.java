package com.example.demo.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 代码提交统计响应数据
 * <AUTHOR>
 * @date 2025-08-20 16:01:37
 */
@Getter
@Setter
public class CommitStatsResponseDTO {
    
    /**
     * 时间周期
     */
    private String period;
    
    /**
     * 团队名称
     */
    private String team;
    
    /**
     * 代码行数
     */
    private Long totalLines;
    
    /**
     * 开发人员数量
     */
    private Integer developerCount;
    
    /**
     * 人均代码行数
     */
    private Long avgLinesPerDeveloper;
    
    /**
     * 增长率
     */
    private Double growthRate;
    
    /**
     * 占比
     */
    private Double percentage;
    
    /**
     * 是否为预估增长率
     */
    private Boolean isEstimated;
    
    /**
     * 同比增长率（与去年同期对比）
     */
    private Double yearOverYearGrowthRate;
}