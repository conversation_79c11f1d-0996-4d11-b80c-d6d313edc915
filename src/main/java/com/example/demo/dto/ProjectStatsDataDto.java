package com.example.demo.dto;

import lombok.Getter;
import lombok.Setter;

/**
 * 项目统计数据DTO
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Getter
@Setter
public class ProjectStatsDataDto {
    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 总问题数量
     */
    private Integer totalIssues;

    /**
     * 开发问题数量
     */
    private Integer devIssues;

    /**
     * 测试问题数量
     */
    private Integer testIssues;

    /**
     * 开发问题分类统计（格式：分类1:数量1\n分类2:数量2）
     */
    private String devIssueCategories;

    /**
     * 测试问题分类统计（格式：分类1:数量1\n分类2:数量2）
     */
    private String testIssueCategories;

    /**
     * 是否有项目分析报告
     */
    private Boolean hasAnalysisReport;

    /**
     * 项目分析报告内容（Markdown格式）
     */
    private String analysisContent;
}
