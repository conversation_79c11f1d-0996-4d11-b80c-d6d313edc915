package com.example.demo.service;

import com.example.demo.dto.CommitStatsRequestDTO;
import com.example.demo.dto.CommitStatsResponseDTO;
import com.example.demo.dto.CommitStatsSummaryDTO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 代码提交统计服务接口
 * <AUTHOR>
 * @date 2025-08-20 16:01:37
 */
public interface CommitStatsService {
    
    /**
     * 获取统计数据（根据维度类型）
     */
    List<CommitStatsResponseDTO> getStatsData(CommitStatsRequestDTO request);
    
    /**
     * 获取年度统计数据
     */
    List<CommitStatsResponseDTO> getYearlyStats(CommitStatsRequestDTO request);
    
    /**
     * 获取半年度统计数据
     */
    List<CommitStatsResponseDTO> getHalfYearlyStats(CommitStatsRequestDTO request);
    
    /**
     * 获取季度统计数据
     */
    List<CommitStatsResponseDTO> getQuarterlyStats(CommitStatsRequestDTO request);
    
    /**
     * 获取团队年度统计数据
     */
    List<CommitStatsResponseDTO> getTeamYearlyStats(CommitStatsRequestDTO request);
    
    /**
     * 获取团队半年度统计数据
     */
    List<CommitStatsResponseDTO> getTeamHalfYearlyStats(CommitStatsRequestDTO request);
    
    /**
     * 获取团队季度统计数据
     */
    List<CommitStatsResponseDTO> getTeamQuarterlyStats(CommitStatsRequestDTO request);
    
    /**
     * 获取统计汇总数据
     */
    List<CommitStatsSummaryDTO> getStatsSummary(CommitStatsRequestDTO request);
    
    /**
     * 获取可用年份列表
     */
    List<Integer> getAvailableYears();
    
    /**
     * 获取所有团队列表
     */
    List<String> getAllTeams();
    
    /**
     * 导出统计数据
     */
    void exportStatsData(CommitStatsRequestDTO request, HttpServletResponse response);
}