package com.example.demo.service;

import java.util.List;
import java.util.Map;

/**
 * 项目验收数据分析服务接口
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
public interface ProjectAcceptanceAnalysisService {

    /**
     * 获取所有项目名列表（去重）
     *
     * @return 项目名列表
     */
    List<String> findDistinctProjectNames();

    /**
     * 根据上线日期获取最新的项目名
     *
     * @return 最新项目名
     */
    String findLatestProjectByOnlineDate();

    /**
     * 获取开发问题分类统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 开发问题分类统计数据
     */
    Map<String, Integer> getDevIssueCategoryStats(String projectName, String grayDateStart, 
                                                  String grayDateEnd, String onlineDateStart, String onlineDateEnd);

    /**
     * 获取测试问题分类统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 测试问题分类统计数据
     */
    Map<String, Integer> getTestIssueCategoryStats(String projectName, String grayDateStart, 
                                                   String grayDateEnd, String onlineDateStart, String onlineDateEnd);

    /**
     * 获取测试人员Bug数统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 测试人员Bug数统计数据
     */
    Map<String, Integer> getTesterBugStats(String projectName, String grayDateStart, 
                                           String grayDateEnd, String onlineDateStart, String onlineDateEnd);

    /**
     * 获取开发人员Bug数统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 开发人员Bug数统计数据
     */
    Map<String, Integer> getDeveloperBugStats(String projectName, String grayDateStart, 
                                              String grayDateEnd, String onlineDateStart, String onlineDateEnd);

    /**
     * 获取开发人员问题分类详细统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 开发人员问题分类详细统计数据 (开发人员 -> 问题分类 -> 数量)
     */
    Map<String, Map<String, Integer>> getDeveloperIssueDetailStats(String projectName, String grayDateStart, 
                                                                   String grayDateEnd, String onlineDateStart, String onlineDateEnd);

    /**
     * 获取测试人员问题分类详细统计数据
     *
     * @param projectName 项目名称
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 测试人员问题分类详细统计数据 (测试人员 -> 问题分类 -> 数量)
     */
    Map<String, Map<String, Integer>> getTesterIssueDetailStats(String projectName, String grayDateStart, 
                                                                String grayDateEnd, String onlineDateStart, String onlineDateEnd);
}
