package com.example.demo.service;

import com.example.demo.entity.primary.DevWorkTimeSpentStats;
import com.example.demo.mapper.primary.DevWorkTimeSpentStatsMapper;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class DevWorkTimeSpentStatsService {
    private final DevWorkTimeSpentStatsMapper workTimeSpentStatsMapper;

    public DevWorkTimeSpentStatsService(DevWorkTimeSpentStatsMapper workTimeSpentStatsMapper) {
        this.workTimeSpentStatsMapper = workTimeSpentStatsMapper;
    }

    public List<DevWorkTimeSpentStats> getWorkTimeStats() {
        return workTimeSpentStatsMapper.getWorkTimeStats();
    }

    public List<String> getDistinctTeams() {
        return workTimeSpentStatsMapper.getDistinctTeams();
    }
} 