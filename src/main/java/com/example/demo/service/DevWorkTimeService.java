package com.example.demo.service;

import com.example.demo.entity.primary.DevWorkTimeSpent;
import com.example.demo.mapper.primary.DevWorkTimeMapper;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class DevWorkTimeService {
    private final DevWorkTimeMapper devWorkTimeMapper;

    public DevWorkTimeService(DevWorkTimeMapper devWorkTimeMapper) {
        this.devWorkTimeMapper = devWorkTimeMapper;
    }

    public List<DevWorkTimeSpent> getWorkTimeByConditions(String team, List<String> usernames, 
                                                        String startMonth, String endMonth) {
        return devWorkTimeMapper.findByConditions(team, usernames, startMonth, endMonth);
    }

    public List<String> getDistinctUsers(String team) {
        return devWorkTimeMapper.findDistinctUsers(team);
    }

    public List<String> getDistinctTeams() {
        return devWorkTimeMapper.findDistinctTeams();
    }
} 