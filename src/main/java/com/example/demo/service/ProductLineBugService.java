package com.example.demo.service;

import com.example.demo.entity.secondary.ProdBug;
import com.example.demo.mapper.secondary.ProdBugMapper;
import org.springframework.stereotype.Service;
import java.util.*;

@Service
public class ProductLineBugService {
    
    private final ProdBugMapper prodBugMapper;

    public ProductLineBugService(ProdBugMapper prodBugMapper) {
        this.prodBugMapper = prodBugMapper;
    }
    
    public Map<String, Map<String, Integer>> getBugStatistics(String startMonth, String endMonth) {
        Map<String, Map<String, Integer>> statistics = new TreeMap<>();
        
        List<ProdBug> bugs = prodBugMapper.findByDateRange(startMonth, endMonth);
        
        bugs.forEach(bug -> {
            if (bug.get月份() != null && bug.get一级分类() != null) {
                statistics.computeIfAbsent(bug.get月份(), k -> new HashMap<>());
                Map<String, Integer> monthStats = statistics.get(bug.get月份());
                monthStats.merge(bug.get一级分类(), 1, Integer::sum);
            }
        });

        return statistics;
    }

    public List<String> getAllCategories() {
        return prodBugMapper.findDistinctCategories();
    }
} 