package com.example.demo.service;

import com.example.demo.entity.primary.DevCommitsMonthly;
import com.example.demo.mapper.primary.DevCommitsMonthlyMapper;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class DevCommitsService {
    private final DevCommitsMonthlyMapper devCommitsMonthlyMapper;

    public DevCommitsService(DevCommitsMonthlyMapper devCommitsMonthlyMapper) {
        this.devCommitsMonthlyMapper = devCommitsMonthlyMapper;
    }

    public List<DevCommitsMonthly> getAllCommits() {
        return devCommitsMonthlyMapper.findAll();
    }

    public List<DevCommitsMonthly> getCommitsByUsernames(List<String> usernames) {
        return devCommitsMonthlyMapper.findByUsernames(usernames);
    }

    public List<DevCommitsMonthly> getCommitsByTeamAndDateRange(String team, String startMonth, String endMonth) {
        return devCommitsMonthlyMapper.findByTeamAndDateRange(team, startMonth, endMonth);
    }

    public List<String> getDistinctUsernames() {
        return devCommitsMonthlyMapper.findDistinctUsernames();
    }

    public List<String> getDistinctMonthly() {
        return devCommitsMonthlyMapper.findDistinctMonthly();
    }

    public List<String> getDistinctTeams() {
        return devCommitsMonthlyMapper.findDistinctTeams();
    }

    public List<String> getUsernamesByTeam(String team) {
        return devCommitsMonthlyMapper.findUsernamesByTeam(team);
    }
} 