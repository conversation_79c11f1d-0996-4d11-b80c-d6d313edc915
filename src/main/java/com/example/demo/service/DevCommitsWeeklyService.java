package com.example.demo.service;

import com.example.demo.entity.primary.DevCommitsWeekly;
import com.example.demo.mapper.primary.DevCommitsWeeklyMapper;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class DevCommitsWeeklyService {
    private final DevCommitsWeeklyMapper devCommitsWeeklyMapper;

    public DevCommitsWeeklyService(DevCommitsWeeklyMapper devCommitsWeeklyMapper) {
        this.devCommitsWeeklyMapper = devCommitsWeeklyMapper;
    }

    public List<DevCommitsWeekly> getCommitsByConditions(String team, List<String> usernames, 
                                                        String startWeek, String endWeek) {
        return devCommitsWeeklyMapper.findByConditions(team, usernames, startWeek, endWeek);
    }

    public List<String> getDistinctWeeks() {
        return devCommitsWeeklyMapper.findDistinctWeeks();
    }

    public List<String> getUsernamesByTeam(String team) {
        return devCommitsWeeklyMapper.findUsernamesByTeam(team);
    }

    public List<String> getDistinctTeams() {
        return devCommitsWeeklyMapper.findDistinctTeams();
    }
} 