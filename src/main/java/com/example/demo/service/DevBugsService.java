package com.example.demo.service;

import com.example.demo.entity.primary.DevBugsOfCommits;
import com.example.demo.mapper.primary.DevBugsMapper;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
public class DevBugsService {
    private final DevBugsMapper devBugsMapper;
    private final DevCommitsService devCommitsService;

    public DevBugsService(DevBugsMapper devBugsMapper, DevCommitsService devCommitsService) {
        this.devBugsMapper = devBugsMapper;
        this.devCommitsService = devCommitsService;
    }

    public List<DevBugsOfCommits> getBugsByConditions(String team, List<String> usernames, 
                                                     String startMonth, String endMonth, String mode) {
        return devBugsMapper.findByConditions(team, usernames, startMonth, endMonth, mode);
    }

    public List<String> getDistinctMonths() {
        return devBugsMapper.findDistinctMonths();
    }

    public List<String> getDistinctTeams() {
        return devCommitsService.getDistinctTeams();
    }

    public List<String> getUsernamesByTeam(String team) {
        return devBugsMapper.findUsernamesByTeam(team);
    }
} 