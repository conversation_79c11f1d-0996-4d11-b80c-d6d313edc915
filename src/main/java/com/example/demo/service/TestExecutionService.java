package com.example.demo.service;

import com.example.demo.entity.primary.TestExecutionStats;
import com.example.demo.entity.primary.TestExecutionQueryParam;
import com.example.demo.mapper.primary.TestTestsetResultMapper;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 自动化测试执行统计服务
 * <AUTHOR>
 * @date 2025-03-13 09:56:46
 */
@Service
public class TestExecutionService {
    private final TestTestsetResultMapper testTestsetResultMapper;

    public TestExecutionService(TestTestsetResultMapper testTestsetResultMapper) {
        this.testTestsetResultMapper = testTestsetResultMapper;
    }

    /**
     * 查询自动化测试执行统计数据
     * @param queryParam 查询参数
     * @return 统计结果列表
     */
    public List<TestExecutionStats> queryTestExecutionStats(TestExecutionQueryParam queryParam) {
        String appName = queryParam.getAppName();
        if ("全部".equals(appName)) {
            appName = null;
        }
        String executeEnv = queryParam.getExecuteEnv();
        if ("全部".equals(executeEnv)) {
            executeEnv = null;
        }
        return testTestsetResultMapper.queryTestExecutionStats(
                queryParam.getStartDate(),
                queryParam.getEndDate(),
                appName,
                executeEnv
        );
    }

    /**
     * 获取所有应用名列表（去重）
     * @return 应用名列表
     */
    public List<String> findDistinctAppNames() {
        return testTestsetResultMapper.findDistinctAppNames();
    }

    /**
     * 获取所有执行环境列表（去重）
     * @return 执行环境列表
     */
    public List<String> findDistinctExecuteEnvs() {
        return testTestsetResultMapper.findDistinctExecuteEnvs();
    }

    /**
     * 获取默认的查询参数
     * @return 默认查询参数
     */
    public TestExecutionQueryParam getDefaultQueryParam() {
        TestExecutionQueryParam queryParam = new TestExecutionQueryParam();
        
        // 设置默认日期范围为今天
        LocalDate yesterday = LocalDate.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        queryParam.setStartDate(yesterday.format(formatter));
        queryParam.setEndDate(yesterday.format(formatter));
        
        // 设置默认应用名为第一个应用名
//        List<String> appNames = findDistinctAppNames();
//        if (!appNames.isEmpty()) {
//            queryParam.setAppName(appNames.get(0));
//        }
        queryParam.setAppName("全部");
        // 默认执行环境为空，表示查询所有环境
        queryParam.setExecuteEnv("全部");
        
        return queryParam;
    }
} 