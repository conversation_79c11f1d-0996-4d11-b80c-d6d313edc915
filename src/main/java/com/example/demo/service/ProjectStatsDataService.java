package com.example.demo.service;

import com.example.demo.dto.ProjectStatsDataDto;

import java.util.List;

/**
 * 项目统计数据服务接口
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
public interface ProjectStatsDataService {

    /**
     * 获取项目统计数据列表
     *
     * @param projectName 项目名称（模糊查询）
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目统计数据列表
     */
    List<ProjectStatsDataDto> getProjectStatsDataList(String projectName, String onlineDateStart, String onlineDateEnd);

    /**
     * 获取项目分析报告内容
     *
     * @param projectName 项目名称
     * @return 项目分析报告内容（Markdown格式）
     */
    String getProjectAnalysisContent(String projectName);
}
