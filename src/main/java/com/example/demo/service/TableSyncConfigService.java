package com.example.demo.service;

import com.example.demo.entity.secondary.TableSyncConfig;

import java.util.List;

/**
 * 表同步配置服务接口
 *
 * <AUTHOR>
 * @date 2024-05-30 07:54:35
 */
public interface TableSyncConfigService {

    /**
     * 查询有效的表同步配置
     *
     * @param sourceDb 源库名称
     * @param sourceTable 源表名称
     * @param targetTable 目标表名称
     * @param syncType 接入方式
     * @return 表同步配置列表
     */
    List<TableSyncConfig> queryValidConfigs(String sourceDb, String sourceTable, String targetTable, String syncType);

    /**
     * 获取所有源库名称
     *
     * @return 源库名称列表
     */
    List<String> getSourceDbs();

    /**
     * 获取所有接入方式
     *
     * @return 接入方式列表
     */
    List<String> getSyncTypes();
    
    /**
     * 根据ID获取表同步配置
     *
     * @param id 配置ID
     * @return 表同步配置信息
     */
    TableSyncConfig getConfigById(Long id);
    
    /**
     * 新增表同步配置
     *
     * @param config 表同步配置信息
     * @return 新增的配置ID
     */
    Long addConfig(TableSyncConfig config);
    
    /**
     * 更新表同步配置
     *
     * @param config 表同步配置信息
     * @return 影响的行数
     */
    int updateConfig(TableSyncConfig config);
    
    /**
     * 逻辑删除表同步配置
     *
     * @param id 配置ID
     * @return 是否删除成功
     */
    boolean logicalDeleteConfig(Long id);
} 