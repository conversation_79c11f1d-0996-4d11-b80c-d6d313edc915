package com.example.demo.service.impl;

import com.example.demo.mapper.primary.ProjectAcceptanceAnalysisMapper;
import com.example.demo.service.ProjectAcceptanceAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目验收数据分析服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Service
public class ProjectAcceptanceAnalysisServiceImpl implements ProjectAcceptanceAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectAcceptanceAnalysisServiceImpl.class);

    @Autowired
    private ProjectAcceptanceAnalysisMapper analysisMapper;

    @Override
    public List<String> findDistinctProjectNames() {
        logger.debug("查询所有项目名列表");
        return analysisMapper.findDistinctProjectNames();
    }

    @Override
    public String findLatestProjectByOnlineDate() {
        logger.debug("查询按上线日期排序的最新项目");
        return analysisMapper.findLatestProjectByOnlineDate();
    }

    @Override
    public Map<String, Integer> getDevIssueCategoryStats(String projectName, String grayDateStart, 
                                                         String grayDateEnd, String onlineDateStart, String onlineDateEnd) {
        logger.info("获取开发问题分类统计，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = analysisMapper.getDevIssueCategoryStats(
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String category = (String) item.get("category");
            Integer count = ((Number) item.get("count")).intValue();
            if (StringUtils.hasText(category)) {
                result.put(category, count);
            }
        }
        
        logger.debug("开发问题分类统计结果：{}", result);
        return result;
    }

    @Override
    public Map<String, Integer> getTestIssueCategoryStats(String projectName, String grayDateStart, 
                                                          String grayDateEnd, String onlineDateStart, String onlineDateEnd) {
        logger.info("获取测试问题分类统计，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = analysisMapper.getTestIssueCategoryStats(
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String category = (String) item.get("category");
            Integer count = ((Number) item.get("count")).intValue();
            if (StringUtils.hasText(category)) {
                result.put(category, count);
            }
        }
        
        logger.debug("测试问题分类统计结果：{}", result);
        return result;
    }

    @Override
    public Map<String, Integer> getTesterBugStats(String projectName, String grayDateStart, 
                                                  String grayDateEnd, String onlineDateStart, String onlineDateEnd) {
        logger.info("获取测试人员Bug数统计，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = analysisMapper.getTesterBugStats(
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String tester = (String) item.get("tester");
            Integer count = ((Number) item.get("count")).intValue();
            if (StringUtils.hasText(tester)) {
                result.put(tester, count);
            }
        }
        
        logger.debug("测试人员Bug数统计结果：{}", result);
        return result;
    }

    @Override
    public Map<String, Integer> getDeveloperBugStats(String projectName, String grayDateStart, 
                                                     String grayDateEnd, String onlineDateStart, String onlineDateEnd) {
        logger.info("获取开发人员Bug数统计，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = analysisMapper.getDeveloperBugStats(
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Integer> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String developer = (String) item.get("developer");
            Integer count = ((Number) item.get("count")).intValue();
            if (StringUtils.hasText(developer)) {
                result.put(developer, count);
            }
        }
        
        logger.debug("开发人员Bug数统计结果：{}", result);
        return result;
    }

    @Override
    public Map<String, Map<String, Integer>> getDeveloperIssueDetailStats(String projectName, String grayDateStart, 
                                                                          String grayDateEnd, String onlineDateStart, String onlineDateEnd) {
        logger.info("获取开发人员问题分类详细统计，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = analysisMapper.getDeveloperIssueDetailStats(
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Map<String, Integer>> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String developer = (String) item.get("developer");
            String category = (String) item.get("category");
            Integer count = ((Number) item.get("count")).intValue();
            
            if (StringUtils.hasText(developer) && StringUtils.hasText(category)) {
                result.computeIfAbsent(developer, k -> new HashMap<>()).put(category, count);
            }
        }
        
        logger.debug("开发人员问题分类详细统计结果：{}", result);
        return result;
    }

    @Override
    public Map<String, Map<String, Integer>> getTesterIssueDetailStats(String projectName, String grayDateStart, 
                                                                       String grayDateEnd, String onlineDateStart, String onlineDateEnd) {
        logger.info("获取测试人员问题分类详细统计，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = analysisMapper.getTesterIssueDetailStats(
                projectName, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Map<String, Integer>> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String tester = (String) item.get("tester");
            String category = (String) item.get("category");
            Integer count = ((Number) item.get("count")).intValue();
            
            if (StringUtils.hasText(tester) && StringUtils.hasText(category)) {
                result.computeIfAbsent(tester, k -> new HashMap<>()).put(category, count);
            }
        }
        
        logger.debug("测试人员问题分类详细统计结果：{}", result);
        return result;
    }
}
