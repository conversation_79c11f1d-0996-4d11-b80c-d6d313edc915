package com.example.demo.service.impl;

import com.alibaba.excel.EasyExcel;
import com.example.demo.entity.primary.GrayData;
import com.example.demo.entity.primary.GrayDataExcelDto;
import com.example.demo.exception.ProjectDuplicateException;
import com.example.demo.mapper.primary.GrayDataMapper;
import com.example.demo.service.GrayDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;

/**
 * 灰度数据服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-09 16:54:24
 */
@Service
public class GrayDataServiceImpl implements GrayDataService {

    private static final Logger logger = LoggerFactory.getLogger(GrayDataServiceImpl.class);

    /**
     * Excel必需字段列表（仅7个必填字段）
     */
    private static final List<String> REQUIRED_EXCEL_FIELDS = Arrays.asList(
        "开发对接人", "开发备注", "开发问题分类", "是否有测试用例",
        "测试问题分类", "测试原因分析", "测试人员"
    );

    @Autowired
    private GrayDataMapper grayDataMapper;
    
    @Override
    public List<GrayData> findByConditions(String projectName, String acceptanceEnv) {
        return grayDataMapper.findByConditions(projectName, acceptanceEnv);
    }
    
    @Override
    public List<String> findDistinctProjectNames() {
        return grayDataMapper.findDistinctProjectNames();
    }
    
    @Override
    public List<String> findDistinctAcceptanceEnvs() {
        return grayDataMapper.findDistinctAcceptanceEnvs();
    }
    
    @Override
    public boolean existsByProjectName(String projectName) {
        return grayDataMapper.countByProjectName(projectName) > 0;
    }

    /**
     * 验证Excel文件字段是否齐全
     *
     * @param file Excel文件
     * @throws Exception 验证失败时抛出异常
     * <AUTHOR>
     * @date 2025-07-10 08:38:04
     */
    private void validateExcelFields(MultipartFile file) throws Exception {
        logger.info("开始验证Excel文件字段");

        try {
            // 先尝试读取一条数据来验证字段
            EasyExcel.read(file.getInputStream())
                    .head(GrayDataExcelDto.class)
                    .sheet()
                    .headRowNumber(1)
                    .doReadSync();

            // 如果能正常读取，说明字段匹配
            logger.info("Excel文件字段验证通过，能够正常解析数据");

        } catch (Exception e) {
            logger.error("Excel文件字段验证失败: {}", e.getMessage());

            // 检查是否是字段不匹配的问题
            String errorMessage = e.getMessage();
            if (errorMessage != null && (errorMessage.contains("head") || errorMessage.contains("字段") || errorMessage.contains("列"))) {
                throw new IllegalArgumentException("Excel文件字段不齐全或字段名不匹配。请确保Excel文件包含以下字段：" +
                    String.join("、", REQUIRED_EXCEL_FIELDS));
            } else {
                throw new Exception("读取Excel文件失败：" + errorMessage, e);
            }
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExcel(MultipartFile file) throws Exception {
        return importExcel(file, false, null, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExcel(MultipartFile file, boolean forceOverwrite) throws Exception {
        return importExcel(file, forceOverwrite, null, null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExcel(MultipartFile file, String grayDate, String onlineDate) throws Exception {
        return importExcel(file, false, grayDate, onlineDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importExcel(MultipartFile file, boolean forceOverwrite, String grayDate, String onlineDate) throws Exception {
        logger.info("开始导入Excel文件，强制覆盖: {}", forceOverwrite);

        if (file == null || file.isEmpty()) {
            logger.error("上传文件为空");
            throw new IllegalArgumentException("上传文件不能为空");
        }

        // 验证Excel文件字段是否齐全
        validateExcelFields(file);

        // 从文件名获取项目名（去掉扩展名）
        String originalFilename = file.getOriginalFilename();
        logger.info("上传文件名: {}", originalFilename);

        if (!StringUtils.hasText(originalFilename)) {
            logger.error("文件名为空");
            throw new IllegalArgumentException("文件名不能为空");
        }

        String projectName = originalFilename;
        if (originalFilename != null) {
            int lastDotIndex = originalFilename.lastIndexOf(".");
            if (lastDotIndex > 0) {
                projectName = originalFilename.substring(0, lastDotIndex);
            }
        }
        logger.info("解析得到项目名: {}", projectName);

        // 检查项目名是否已存在数据
        int existingRecordCount = grayDataMapper.countByProjectName(projectName);
        if (existingRecordCount > 0 && !forceOverwrite) {
            logger.warn("项目【{}】已存在 {} 条数据，需要用户确认是否覆盖", projectName, existingRecordCount);
            throw new ProjectDuplicateException(projectName, existingRecordCount);
        }

        // 如果需要覆盖且存在数据，先删除现有数据
        if (forceOverwrite && existingRecordCount > 0) {
            logger.info("开始删除项目【{}】的现有数据，共 {} 条记录", projectName, existingRecordCount);
            int deletedCount = grayDataMapper.deleteByProjectName(projectName);
            logger.info("删除完成，实际删除 {} 条记录", deletedCount);
        }

        // 读取Excel数据
        logger.info("开始读取Excel文件数据");
        List<GrayDataExcelDto> excelDataList;
        try {
            excelDataList = EasyExcel.read(file.getInputStream())
                    .head(GrayDataExcelDto.class)
                    .sheet()
                    .doReadSync();
            logger.info("Excel文件读取成功，共读取到 {} 条数据", excelDataList != null ? excelDataList.size() : 0);
        } catch (IOException e) {
            logger.error("读取Excel文件失败: {}", e.getMessage(), e);
            throw new Exception("读取Excel文件失败：" + e.getMessage(), e);
        }

        if (excelDataList == null || excelDataList.isEmpty()) {
            logger.warn("Excel文件中没有数据");
            throw new IllegalArgumentException("Excel文件中没有数据");
        }

        // 转换为实体对象
        logger.info("开始转换Excel数据为实体对象");
        List<GrayData> grayDataList = new ArrayList<>();
        Date now = new Date();

        for (GrayDataExcelDto excelDto : excelDataList) {
            GrayData grayData = new GrayData();
            grayData.setProjectName(projectName);
            grayData.setAcceptanceEnv(excelDto.getAcceptanceEnv());
            grayData.setPlatform(excelDto.getPlatform());
            grayData.setIssueType(excelDto.getIssueType());
            grayData.setModule(excelDto.getModule());
            grayData.setPage(excelDto.getPage());
            grayData.setIssueDescription(excelDto.getIssueDescription());
            grayData.setModifyProgress(excelDto.getModifyProgress());
            grayData.setCurrentTestable(excelDto.getCurrentTestable());
            grayData.setAcceptor(excelDto.getAcceptor());
            grayData.setIssueAttribution(excelDto.getIssueAttribution());
            // 对开发对接人字段进行姓名格式化处理
            grayData.setDevContact(formatNameField(excelDto.getDevContact()));
            grayData.setDevRemark(excelDto.getDevRemark());
            grayData.setDevIssueCategory(excelDto.getDevIssueCategory());
            grayData.setHasTestCase(excelDto.getHasTestCase());
            grayData.setTestIssueCategory(excelDto.getTestIssueCategory());
            grayData.setTestReasonAnalysis(excelDto.getTestReasonAnalysis());
            // 对测试人员字段进行姓名格式化处理
            grayData.setTester(formatNameField(excelDto.getTester()));
            // 日期从参数中获取，而不是从Excel中获取
            grayData.setGrayDate(grayDate);
            grayData.setOnlineDate(onlineDate);
            grayData.setCreateTime(now);
            grayData.setUpdateTime(now);

            grayDataList.add(grayData);
        }
        logger.info("数据转换完成，共转换 {} 条记录", grayDataList.size());

        // 批量插入数据
        logger.info("开始批量插入数据到数据库");
        int insertCount = grayDataMapper.batchInsert(grayDataList);
        logger.info("数据插入完成，实际插入 {} 条记录", insertCount);

        String resultMessage;
        if (forceOverwrite && existingRecordCount > 0) {
            resultMessage = "成功覆盖导入项目【" + projectName + "】的数据，删除 " + existingRecordCount + " 条旧记录，新增 " + insertCount + " 条记录";
        } else {
            resultMessage = "成功导入项目【" + projectName + "】的数据，共 " + insertCount + " 条记录";
        }
        logger.info("Excel导入完成: {}", resultMessage);
        return resultMessage;
    }

    /**
     * 格式化姓名字段
     * 1. 去除开头@符号：如果姓名字符串的第一个字符是@符号，则直接删除该@符号
     * 2. 替换中间@符号为逗号：如果姓名字符串中间位置包含@符号，则将所有@符号替换为英文逗号","
     *
     * @param nameField 原始姓名字段
     * @return 格式化后的姓名字段
     * <AUTHOR>
     * @date 2025-07-11 12:30:29
     */
    private String formatNameField(String nameField) {
        if (nameField == null || nameField.trim().isEmpty()) {
            return nameField;
        }

        String trimmedName = nameField.trim();

        // 1. 去除开头@符号
        if (trimmedName.startsWith("@")) {
            trimmedName = trimmedName.substring(1);
        }

        // 2. 替换中间@符号为逗号
        if (trimmedName.contains("@")) {
            trimmedName = trimmedName.replace("@", ",");
        }

        return trimmedName;
    }
}
