package com.example.demo.service.impl;

import com.example.demo.dto.ProjectStatsDataDto;
import com.example.demo.mapper.primary.ProjectStatsDataMapper;
import com.example.demo.service.ProjectStatsDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目统计数据服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Service
public class ProjectStatsDataServiceImpl implements ProjectStatsDataService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectStatsDataServiceImpl.class);

    @Autowired
    private ProjectStatsDataMapper projectStatsDataMapper;

    @Override
    public List<ProjectStatsDataDto> getProjectStatsDataList(String projectName, String onlineDateStart, String onlineDateEnd) {
        logger.info("获取项目统计数据列表，参数：项目名={}, 上线日期区间=[{}, {}]",
                projectName, onlineDateStart, onlineDateEnd);

        // 统计逻辑说明：
        // 1. 排除修改进度为"无需修复"和"下个版本"的记录
        // 2. 开发问题数量：只统计开发对接人不为空的记录
        // 3. 测试问题数量：只统计测试人员不为空的记录
        // 修改时间：2025-07-11 13:24:44

        List<Map<String, Object>> rawData = projectStatsDataMapper.getProjectStatsData(
                projectName, onlineDateStart, onlineDateEnd);

        List<ProjectStatsDataDto> result = new ArrayList<>();
        for (Map<String, Object> item : rawData) {
            ProjectStatsDataDto dto = new ProjectStatsDataDto();
            dto.setProjectName((String) item.get("project_name"));
            dto.setTotalIssues(((Number) item.get("total_issues")).intValue());
            dto.setDevIssues(((Number) item.get("dev_issues")).intValue());
            dto.setTestIssues(((Number) item.get("test_issues")).intValue());

            // 获取开发问题分类统计
            String devCategories = buildCategoryString(
                    projectStatsDataMapper.getProjectDevIssueCategories(dto.getProjectName()));
            dto.setDevIssueCategories(devCategories);

            // 获取测试问题分类统计
            String testCategories = buildCategoryString(
                    projectStatsDataMapper.getProjectTestIssueCategories(dto.getProjectName()));
            dto.setTestIssueCategories(testCategories);

            // 检查是否有分析报告
            Integer reportCount = projectStatsDataMapper.checkProjectAnalysisExists(dto.getProjectName());
            dto.setHasAnalysisReport(reportCount != null && reportCount > 0);

            result.add(dto);
        }

        logger.debug("项目统计数据列表查询结果：共{}条记录", result.size());
        return result;
    }

    @Override
    public String getProjectAnalysisContent(String projectName) {
        logger.info("获取项目分析报告内容，项目名：{}", projectName);
        
        if (!StringUtils.hasText(projectName)) {
            logger.warn("项目名称为空，无法获取分析报告");
            return null;
        }

        String content = projectStatsDataMapper.getProjectAnalysisContent(projectName);
        logger.debug("项目分析报告内容获取完成，内容长度：{}", content != null ? content.length() : 0);
        return content;
    }

    /**
     * 构建分类统计字符串
     *
     * @param categoryData 分类数据
     * @return 格式化的分类字符串
     */
    private String buildCategoryString(List<Map<String, Object>> categoryData) {
        if (categoryData == null || categoryData.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (Map<String, Object> item : categoryData) {
            String category = (String) item.get("category");
            Integer count = ((Number) item.get("count")).intValue();
            if (StringUtils.hasText(category)) {
                if (sb.length() > 0) {
                    sb.append("\n");
                }
                sb.append(category).append(": ").append(count);
            }
        }
        return sb.toString();
    }
}
