package com.example.demo.service.impl;

import com.example.demo.dto.PersonalCommitStatsRequestDTO;
import com.example.demo.dto.PersonalCommitStatsResponseDTO;
import com.example.demo.mapper.primary.PersonalCommitStatsMapper;
import com.example.demo.service.PersonalCommitStatsService;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 个人代码提交统计服务实现类
 * <AUTHOR>
 * @date 2025-08-23 10:15:55
 */
@Service
public class PersonalCommitStatsServiceImpl implements PersonalCommitStatsService {
    
    private final PersonalCommitStatsMapper personalCommitStatsMapper;
    
    public PersonalCommitStatsServiceImpl(PersonalCommitStatsMapper personalCommitStatsMapper) {
        this.personalCommitStatsMapper = personalCommitStatsMapper;
    }
    
    @Override
    public List<PersonalCommitStatsResponseDTO> getPersonalStatsData(PersonalCommitStatsRequestDTO request) {
        String dimensionType = request.getDimensionType();
        if (dimensionType == null) {
            dimensionType = "YEAR";
        }
        
        switch (dimensionType) {
            case "HALF_YEAR":
                return getPersonalHalfYearlyStats(request);
            case "QUARTER":
                return getPersonalQuarterlyStats(request);
            case "TOP_RANKING":
                return getTopDevelopersRanking(request);
            default:
                return getPersonalYearlyStats(request);
        }
    }
    
    @Override
    public List<PersonalCommitStatsResponseDTO> getPersonalYearlyStats(PersonalCommitStatsRequestDTO request) {
        List<PersonalCommitStatsResponseDTO> result = personalCommitStatsMapper.selectPersonalYearlyStats(request);
        calculatePersonalGrowthRate(result);
        calculatePersonalRankings(result);
        calculatePersonalYearOverYearGrowthRate(result, false);
        return result;
    }
    
    @Override
    public List<PersonalCommitStatsResponseDTO> getPersonalHalfYearlyStats(PersonalCommitStatsRequestDTO request) {
        List<PersonalCommitStatsResponseDTO> result = personalCommitStatsMapper.selectPersonalHalfYearlyStats(request);
        calculatePersonalGrowthRate(result);
        calculatePersonalRankings(result);
        calculatePersonalYearOverYearGrowthRate(result, true);
        return result;
    }
    
    @Override
    public List<PersonalCommitStatsResponseDTO> getPersonalQuarterlyStats(PersonalCommitStatsRequestDTO request) {
        List<PersonalCommitStatsResponseDTO> result = personalCommitStatsMapper.selectPersonalQuarterlyStats(request);
        calculatePersonalGrowthRate(result);
        calculatePersonalRankings(result);
        calculatePersonalYearOverYearGrowthRate(result, false);
        return result;
    }
    
    @Override
    public List<PersonalCommitStatsResponseDTO> getTopDevelopersRanking(PersonalCommitStatsRequestDTO request) {
        List<PersonalCommitStatsResponseDTO> result = personalCommitStatsMapper.selectTopDevelopersRanking(request);
        calculateGlobalRankings(result);
        calculateTeamPercentages(result);
        return result;
    }
    
    @Override
    public List<String> getAllDevelopers() {
        return personalCommitStatsMapper.selectAllDevelopers();
    }
    
    @Override
    public List<String> getDevelopersByTeams(List<String> teams) {
        return personalCommitStatsMapper.selectDevelopersByTeams(teams);
    }
    
    /**
     * 计算个人增长率
     * 按用户分组，为每个用户计算时间序列的增长率
     * <AUTHOR>
     * @date 2025-08-23 10:15:55
     */
    private void calculatePersonalGrowthRate(List<PersonalCommitStatsResponseDTO> dataList) {
        if (dataList.isEmpty()) {
            return;
        }
        
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        int currentMonth = currentDate.getMonthValue();
        int completeMonth = currentMonth - 1;
        
        // 按用户分组
        Map<String, List<PersonalCommitStatsResponseDTO>> userGroupedData = dataList.stream()
                .collect(Collectors.groupingBy(PersonalCommitStatsResponseDTO::getUsername));
        
        // 为每个用户分别计算增长率
        userGroupedData.forEach((username, userData) -> {
            if (userData.size() <= 1) {
                return; // 只有一个数据点，无法计算增长率
            }
            
            // 按时间周期排序
            userData.sort((a, b) -> a.getPeriod().compareTo(b.getPeriod()));
            
            // 计算该用户的增长率
            for (int i = 1; i < userData.size(); i++) {
                PersonalCommitStatsResponseDTO current = userData.get(i);
                PersonalCommitStatsResponseDTO previous = userData.get(i - 1);
                
                if (previous.getTotalLines() != null && previous.getTotalLines() > 0) {
                    long currentLines = current.getTotalLines();
                    long previousLines = previous.getTotalLines();
                    
                    // 判断是否需要预估（参考现有的预估逻辑）
                    boolean isLastRecord = (i == userData.size() - 1);
                    boolean needsEstimation = false;
                    double estimationFactor = 1.0;
                    
                    // 默认设置为非预估
                    current.setIsEstimated(false);
                    
                    if (isLastRecord) {
                        String period = current.getPeriod();
                        if (period != null) {
                            if (period.contains("Q")) {
                                // 季度数据预估
                                needsEstimation = isIncompleteQuarterData(period, currentYear, completeMonth);
                                if (needsEstimation) {
                                    estimationFactor = getQuarterEstimationFactor(period, currentYear, completeMonth);
                                }
                            } else if (period.contains("H")) {
                                // 半年度数据预估
                                needsEstimation = isIncompleteHalfYearData(period, currentYear, completeMonth);
                                if (needsEstimation) {
                                    estimationFactor = getHalfYearEstimationFactor(period, currentYear, completeMonth);
                                }
                            } else if (period.matches("\\d{4}")) {
                                // 年度数据预估
                                int dataYear = Integer.parseInt(period);
                                if (dataYear == currentYear && completeMonth < 12 && completeMonth > 0) {
                                    needsEstimation = true;
                                    // 预估因子 = 总月份数 / 已完成月份数
                                    estimationFactor = 12.0 / completeMonth;
                                }
                            }
                        }
                    }
                    
                    // 计算增长率（如果需要预估，仅在计算增长率时使用预估值，不修改显示数据）
                    long estimatedCurrentLines = needsEstimation ? Math.round(currentLines * estimationFactor) : currentLines;
                    double growthRate = ((estimatedCurrentLines - previousLines) * 100.0) / previousLines;
                    current.setGrowthRate(Math.round(growthRate * 100.0) / 100.0);
                    current.setIsEstimated(needsEstimation);
                } else {
                    // 如果前一期数据为0或null，设置增长率为0
                    current.setGrowthRate(0.0);
                    current.setIsEstimated(false);
                }
            }
        });
    }
    
    /**
     * 判断季度数据是否不完整
     */
    private boolean isIncompleteQuarterData(String period, int currentYear, int completeMonth) {
        if (!period.contains("Q") || !period.startsWith(String.valueOf(currentYear))) {
            return false;
        }
        
        String quarterStr = period.substring(period.indexOf("Q") + 1);
        try {
            int quarter = Integer.parseInt(quarterStr);
            int currentQuarter = (completeMonth - 1) / 3 + 1;
            
            // 只有当前季度才需要预估
            if (quarter != currentQuarter) {
                return false;
            }
            
            int quarterEndMonth = quarter * 3;
            return completeMonth < quarterEndMonth;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 计算季度数据的预估因子
     */
    private double getQuarterEstimationFactor(String period, int currentYear, int completeMonth) {
        String quarterStr = period.substring(period.indexOf("Q") + 1);
        try {
            int quarter = Integer.parseInt(quarterStr);
            int quarterStartMonth = (quarter - 1) * 3 + 1;
            
            // 计算当前季度内已完成的月份数
            int completedMonthsInQuarter = Math.max(1, completeMonth - quarterStartMonth + 1);
            
            // 预估因子 = 季度总月份数 / 已完成月份数
            return 3.0 / completedMonthsInQuarter;
        } catch (NumberFormatException e) {
            return 1.0;
        }
    }
    
    /**
     * 判断半年度数据是否不完整
     */
    private boolean isIncompleteHalfYearData(String period, int currentYear, int completeMonth) {
        if (!period.contains("H") || !period.startsWith(String.valueOf(currentYear))) {
            return false;
        }
        
        String halfYearStr = period.substring(period.indexOf("H") + 1);
        try {
            int halfYear = Integer.parseInt(halfYearStr);
            int currentHalfYear = completeMonth <= 6 ? 1 : 2;
            
            if (halfYear != currentHalfYear) {
                return false;
            }
            
            int halfYearEndMonth = halfYear == 1 ? 6 : 12;
            return completeMonth < halfYearEndMonth;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 计算半年度数据的预估因子
     */
    private double getHalfYearEstimationFactor(String period, int currentYear, int completeMonth) {
        String halfYearStr = period.substring(period.indexOf("H") + 1);
        try {
            int halfYear = Integer.parseInt(halfYearStr);
            
            int completedMonthsInHalfYear;
            if (halfYear == 1) {
                // 上半年：1-6月，已完成月份数
                completedMonthsInHalfYear = Math.max(1, Math.min(completeMonth, 6));
            } else {
                // 下半年：7-12月，已完成月份数
                completedMonthsInHalfYear = Math.max(1, completeMonth - 6);
            }
            
            // 预估因子 = 半年总月份数 / 已完成月份数
            return 6.0 / completedMonthsInHalfYear;
        } catch (NumberFormatException e) {
            return 1.0;
        }
    }
    
    /**
     * 计算个人排名
     */
    private void calculatePersonalRankings(List<PersonalCommitStatsResponseDTO> dataList) {
        // 按时间周期分组
        Map<String, List<PersonalCommitStatsResponseDTO>> periodGroupedData = dataList.stream()
                .collect(Collectors.groupingBy(PersonalCommitStatsResponseDTO::getPeriod));
        
        // 为每个时间周期计算排名
        periodGroupedData.forEach((period, periodData) -> {
            // 全公司排名
            periodData.sort((a, b) -> Long.compare(b.getTotalLines(), a.getTotalLines()));
            AtomicInteger globalRank = new AtomicInteger(1);
            periodData.forEach(item -> item.setGlobalRanking(globalRank.getAndIncrement()));
            
            // 团队内排名
            Map<String, List<PersonalCommitStatsResponseDTO>> teamGroupedData = periodData.stream()
                    .collect(Collectors.groupingBy(PersonalCommitStatsResponseDTO::getTeam));
            
            teamGroupedData.forEach((team, teamData) -> {
                teamData.sort((a, b) -> Long.compare(b.getTotalLines(), a.getTotalLines()));
                AtomicInteger teamRank = new AtomicInteger(1);
                teamData.forEach(item -> item.setTeamRanking(teamRank.getAndIncrement()));
            });
        });
    }
    
    /**
     * 计算全公司排名
     */
    private void calculateGlobalRankings(List<PersonalCommitStatsResponseDTO> dataList) {
        AtomicInteger rank = new AtomicInteger(1);
        dataList.forEach(item -> item.setGlobalRanking(rank.getAndIncrement()));
    }
    
    /**
     * 计算团队占比
     */
    private void calculateTeamPercentages(List<PersonalCommitStatsResponseDTO> dataList) {
        Map<String, Long> teamTotals = dataList.stream()
                .collect(Collectors.groupingBy(
                        PersonalCommitStatsResponseDTO::getTeam,
                        Collectors.summingLong(PersonalCommitStatsResponseDTO::getTotalLines)
                ));
        
        dataList.forEach(item -> {
            Long teamTotal = teamTotals.get(item.getTeam());
            if (teamTotal != null && teamTotal > 0) {
                double percentage = (item.getTotalLines() * 100.0) / teamTotal;
                item.setTeamPercentage(Math.round(percentage * 100.0) / 100.0);
            }
        });
    }
    
    /**
     * 计算个人同比增长率
     * @param dataList 数据列表
     * @param isHalfYear 是否为半年度数据
     * <AUTHOR>
     * @date 2025-08-23 12:45:45
     */
    private void calculatePersonalYearOverYearGrowthRate(List<PersonalCommitStatsResponseDTO> dataList, boolean isHalfYear) {
        if (dataList.isEmpty()) {
            return;
        }
        
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        int currentMonth = currentDate.getMonthValue();
        int completeMonth = currentMonth - 1;
        
        // 按用户分组
        Map<String, List<PersonalCommitStatsResponseDTO>> userGroupedData = dataList.stream()
                .collect(Collectors.groupingBy(PersonalCommitStatsResponseDTO::getUsername));
        
        userGroupedData.forEach((username, userData) -> {
            // 按时间周期排序
            userData.sort((a, b) -> a.getPeriod().compareTo(b.getPeriod()));
            
            for (PersonalCommitStatsResponseDTO current : userData) {
                String currentPeriod = current.getPeriod();
                if (currentPeriod == null) {
                    continue;
                }
                
                String lastYearPeriod = calculateLastYearSamePeriod(currentPeriod, isHalfYear);
                if (lastYearPeriod == null) {
                    continue;
                }
                
                // 查找去年同期数据
                PersonalCommitStatsResponseDTO lastYearData = userData.stream()
                        .filter(item -> lastYearPeriod.equals(item.getPeriod()))
                        .findFirst()
                        .orElse(null);
                
                if (lastYearData != null && lastYearData.getTotalLines() != null && lastYearData.getTotalLines() > 0) {
                    long currentLines = current.getTotalLines();
                    long lastYearLines = lastYearData.getTotalLines();
                    
                    // 判断当前数据是否需要预估（使用与增长率计算相同的逻辑）
                    boolean needsEstimation = false;
                    double estimationFactor = 1.0;
                    
                    if (currentPeriod != null) {
                        if (currentPeriod.contains("Q")) {
                            // 季度数据预估
                            needsEstimation = isIncompleteQuarterData(currentPeriod, currentYear, completeMonth);
                            if (needsEstimation) {
                                estimationFactor = getQuarterEstimationFactor(currentPeriod, currentYear, completeMonth);
                            }
                        } else if (currentPeriod.contains("H")) {
                            // 半年度数据预估
                            needsEstimation = isIncompleteHalfYearData(currentPeriod, currentYear, completeMonth);
                            if (needsEstimation) {
                                estimationFactor = getHalfYearEstimationFactor(currentPeriod, currentYear, completeMonth);
                            }
                        } else if (currentPeriod.matches("\\d{4}")) {
                            // 年度数据预估
                            int dataYear = Integer.parseInt(currentPeriod);
                            if (dataYear == currentYear && completeMonth < 12 && completeMonth > 0) {
                                needsEstimation = true;
                                // 预估因子 = 总月份数 / 已完成月份数
                                estimationFactor = 12.0 / completeMonth;
                            }
                        }
                    }
                    
                    // 计算同比增长率（如果需要预估，使用预估值计算同比增长率）
                    long estimatedCurrentLines = needsEstimation ? Math.round(currentLines * estimationFactor) : currentLines;
                    double yoyGrowthRate = ((estimatedCurrentLines - lastYearLines) * 100.0) / lastYearLines;
                    current.setYearOverYearGrowthRate(Math.round(yoyGrowthRate * 100.0) / 100.0);
                }
            }
        });
    }
    
    /**
     * 计算去年同期周期标识
     * @param currentPeriod 当前周期
     * @param isHalfYear 是否为半年度数据
     * @return 去年同期周期标识
     * <AUTHOR>
     * @date 2025-08-23 12:45:45
     */
    private String calculateLastYearSamePeriod(String currentPeriod, boolean isHalfYear) {
        try {
            // 半年度格式：2024-H1, 2024-H2
            if (isHalfYear && currentPeriod.contains("H")) {
                String[] parts = currentPeriod.split("-H");
                if (parts.length == 2) {
                    int year = Integer.parseInt(parts[0]);
                    String halfYear = parts[1];
                    return (year - 1) + "-H" + halfYear;
                }
            } 
            // 季度格式：2024-Q1, 2024-Q2, 2024-Q3, 2024-Q4
            else if (!isHalfYear && currentPeriod.contains("Q")) {
                String[] parts = currentPeriod.split("-Q");
                if (parts.length == 2) {
                    int year = Integer.parseInt(parts[0]);
                    String quarter = parts[1];
                    return (year - 1) + "-Q" + quarter;
                }
            }
            // 年度格式：2024
            else if (currentPeriod.matches("\\d{4}")) {
                int year = Integer.parseInt(currentPeriod);
                return String.valueOf(year - 1);
            }
        } catch (NumberFormatException e) {
            // 解析失败，返回null
        }
        return null;
    }
}