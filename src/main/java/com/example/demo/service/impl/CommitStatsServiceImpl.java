package com.example.demo.service.impl;

import com.alibaba.excel.EasyExcel;
import com.example.demo.dto.CommitStatsRequestDTO;
import com.example.demo.dto.CommitStatsResponseDTO;
import com.example.demo.dto.CommitStatsSummaryDTO;
import com.example.demo.mapper.primary.CommitStatsMapper;
import com.example.demo.service.CommitStatsService;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 代码提交统计服务实现类
 * <AUTHOR>
 * @date 2025-08-20 16:01:37
 */
@Service
public class CommitStatsServiceImpl implements CommitStatsService {
    
    private final CommitStatsMapper commitStatsMapper;
    
    public CommitStatsServiceImpl(CommitStatsMapper commitStatsMapper) {
        this.commitStatsMapper = commitStatsMapper;
    }
    
    @Override
    public List<CommitStatsResponseDTO> getStatsData(CommitStatsRequestDTO request) {
        String dimensionType = request.getDimensionType();
        if (dimensionType == null) {
            dimensionType = "YEAR";
        }
        
        switch (dimensionType) {
            case "HALF_YEAR":
                return getHalfYearlyStats(request);
            case "QUARTER":
                return getQuarterlyStats(request);
            case "TEAM_YEAR":
                return getTeamYearlyStats(request);
            case "TEAM_HALF_YEAR":
                return getTeamHalfYearlyStats(request);
            case "TEAM_QUARTER":
                return getTeamQuarterlyStats(request);
            default:
                return getYearlyStats(request);
        }
    }
    
    @Override
    public List<CommitStatsResponseDTO> getYearlyStats(CommitStatsRequestDTO request) {
        List<CommitStatsResponseDTO> result = commitStatsMapper.selectYearlyStats(request);
        calculateGrowthRate(result);
        return result;
    }
    
    @Override
    public List<CommitStatsResponseDTO> getHalfYearlyStats(CommitStatsRequestDTO request) {
        List<CommitStatsResponseDTO> result = commitStatsMapper.selectHalfYearlyStats(request);
        calculateGrowthRate(result);
        calculateHalfYearOverYearGrowthRate(result);
        return result;
    }
    
    @Override
    public List<CommitStatsResponseDTO> getQuarterlyStats(CommitStatsRequestDTO request) {
        List<CommitStatsResponseDTO> result = commitStatsMapper.selectQuarterlyStats(request);
        calculateGrowthRate(result);
        calculateYearOverYearGrowthRate(result);
        return result;
    }
    
    @Override
    public List<CommitStatsResponseDTO> getTeamYearlyStats(CommitStatsRequestDTO request) {
        List<CommitStatsResponseDTO> result = commitStatsMapper.selectTeamYearlyStats(request);
        calculateTeamPercentage(result);
        calculateTeamGrowthRate(result);
        return result;
    }
    
    @Override
    public List<CommitStatsResponseDTO> getTeamHalfYearlyStats(CommitStatsRequestDTO request) {
        List<CommitStatsResponseDTO> result = commitStatsMapper.selectTeamHalfYearlyStats(request);
        calculateTeamPercentage(result);
        calculateTeamGrowthRate(result);
        return result;
    }
    
    @Override
    public List<CommitStatsResponseDTO> getTeamQuarterlyStats(CommitStatsRequestDTO request) {
        List<CommitStatsResponseDTO> result = commitStatsMapper.selectTeamQuarterlyStats(request);
        calculateTeamPercentage(result);
        calculateTeamGrowthRate(result);
        return result;
    }
    
    @Override
    public List<CommitStatsSummaryDTO> getStatsSummary(CommitStatsRequestDTO request) {
        return commitStatsMapper.selectStatsSummary(request);
    }
    
    @Override
    public List<Integer> getAvailableYears() {
        return commitStatsMapper.selectAvailableYears();
    }
    
    @Override
    public List<String> getAllTeams() {
        return commitStatsMapper.selectAllTeams();
    }
    
    @Override
    public void exportStatsData(CommitStatsRequestDTO request, HttpServletResponse response) {
        try {
            List<CommitStatsResponseDTO> data = getStatsData(request);
            
            String fileName = "代码提交统计_" + System.currentTimeMillis() + ".xlsx";
            response.setContentType("application/vnd.ms-excel");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            
            EasyExcel.write(response.getOutputStream(), CommitStatsResponseDTO.class)
                    .sheet("代码提交统计")
                    .doWrite(data);
        } catch (IOException e) {
            throw new RuntimeException("导出Excel失败", e);
        }
    }
    
    /**
     * 计算增长率
     */
    private void calculateGrowthRate(List<CommitStatsResponseDTO> dataList) {
        if (dataList.size() <= 1) {
            return;
        }
        
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        int currentMonth = currentDate.getMonthValue();
        // 当前月份的数据下个月才统计出来，所以使用上个月作为已统计的完整月份
        int completeMonth = currentMonth - 1;
        
        for (int i = 1; i < dataList.size(); i++) {
            CommitStatsResponseDTO current = dataList.get(i);
            CommitStatsResponseDTO previous = dataList.get(i - 1);
            
            if (previous.getTotalLines() != null && previous.getTotalLines() > 0) {
                long currentLines = current.getTotalLines();
                long previousLines = previous.getTotalLines();
                
                // 判断是否是最后一条数据且可能是不完整的数据
                boolean isLastRecord = (i == dataList.size() - 1);
                boolean needsEstimation = false;
                double estimationFactor = 1.0;
                
                // 默认设置为非预估
                current.setIsEstimated(false);
                
                if (isLastRecord) {
                    // 判断是否需要预估计算
                    String period = current.getPeriod();
                    if (period != null) {
                        if (period.contains("Q")) {
                            // 季度数据预估
                            needsEstimation = isIncompleteQuarterData(period, currentYear, completeMonth);
                            if (needsEstimation) {
                                estimationFactor = getQuarterEstimationFactor(period, currentYear, completeMonth);
                            }
                        } else if (period.contains("H")) {
                            // 半年度数据预估
                            needsEstimation = isIncompleteHalfYearData(period, currentYear, completeMonth);
                            if (needsEstimation) {
                                estimationFactor = getHalfYearEstimationFactor(period, currentYear, completeMonth);
                            }
                        } else if (period.matches("\\d{4}")) {
                            // 年度数据预估
                            int dataYear = Integer.parseInt(period);
                            if (dataYear == currentYear && completeMonth < 12 && completeMonth > 0) {
                                needsEstimation = true;
                                estimationFactor = 12.0 / completeMonth;
                            }
                        }
                    }
                }
                
                // 如果需要预估，调整当前数据
                if (needsEstimation) {
                    currentLines = Math.round(currentLines * estimationFactor);
                    current.setIsEstimated(true);
                } else {
                    current.setIsEstimated(false);
                }
                
                double growthRate = ((currentLines - previousLines) * 100.0) / previousLines;
                current.setGrowthRate(Math.round(growthRate * 100.0) / 100.0);
            }
        }
    }
    
    /**
     * 判断季度数据是否不完整
     */
    private boolean isIncompleteQuarterData(String period, int currentYear, int completeMonth) {
        if (!period.contains("Q") || !period.startsWith(String.valueOf(currentYear))) {
            return false;
        }
        
        String quarterStr = period.substring(period.indexOf("Q") + 1);
        try {
            int quarter = Integer.parseInt(quarterStr);
            int quarterEndMonth = quarter * 3;
            // 如果已完成月份小于季度结束月份，说明数据不完整
            return completeMonth < quarterEndMonth;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 计算季度数据的预估因子
     */
    private double getQuarterEstimationFactor(String period, int currentYear, int completeMonth) {
        String quarterStr = period.substring(period.indexOf("Q") + 1);
        try {
            int quarter = Integer.parseInt(quarterStr);
            int quarterStartMonth = (quarter - 1) * 3 + 1;
            int quarterEndMonth = quarter * 3;
            
            if (completeMonth < quarterStartMonth) {
                // 已完成月份还没到这个季度，无法预估
                return 1.0;
            }
            
            if (completeMonth >= quarterEndMonth) {
                // 已完成月份已经覆盖这个季度，数据应该是完整的
                return 1.0;
            }
            
            // 计算当前季度已经完成的月份数
            int completedMonths = completeMonth - quarterStartMonth + 1;
            return 3.0 / completedMonths;
        } catch (NumberFormatException e) {
            return 1.0;
        }
    }
    
    /**
     * 计算团队占比
     */
    private void calculateTeamPercentage(List<CommitStatsResponseDTO> dataList) {
        Map<String, Long> periodTotals = dataList.stream()
                .collect(Collectors.groupingBy(
                        CommitStatsResponseDTO::getPeriod,
                        Collectors.summingLong(CommitStatsResponseDTO::getTotalLines)
                ));
        
        dataList.forEach(item -> {
            Long periodTotal = periodTotals.get(item.getPeriod());
            if (periodTotal != null && periodTotal > 0) {
                double percentage = (item.getTotalLines() * 100.0) / periodTotal;
                item.setPercentage(Math.round(percentage * 100.0) / 100.0);
            }
        });
    }

    /**
     * 计算团队增长率
     * <AUTHOR>
     * @date 2025-08-20 19:37:36
     */
    private void calculateTeamGrowthRate(List<CommitStatsResponseDTO> dataList) {
        if (dataList.isEmpty()) {
            return;
        }

        // 按团队分组
        Map<String, List<CommitStatsResponseDTO>> teamGroupedData = dataList.stream()
                .collect(Collectors.groupingBy(
                        item -> item.getTeam() != null ? item.getTeam() : "未分配团队",
                        Collectors.toList()
                ));

        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        int currentMonth = currentDate.getMonthValue();
        int completeMonth = currentMonth - 1;

        // 对每个团队分别计算增长率
        teamGroupedData.forEach((team, teamData) -> {
            // 按时间周期排序
            teamData.sort((a, b) -> {
                String periodA = a.getPeriod();
                String periodB = b.getPeriod();
                
                // 处理年度格式（如"2023"）和季度格式（如"2023-Q1"）
                if (periodA.contains("Q") && periodB.contains("Q")) {
                    // 都是季度格式，直接比较
                    return periodA.compareTo(periodB);
                } else if (!periodA.contains("Q") && !periodB.contains("Q")) {
                    // 都是年度格式，按数字比较
                    try {
                        return Integer.compare(Integer.parseInt(periodA), Integer.parseInt(periodB));
                    } catch (NumberFormatException e) {
                        return periodA.compareTo(periodB);
                    }
                } else {
                    // 混合格式，直接字符串比较
                    return periodA.compareTo(periodB);
                }
            });

            // 计算该团队的增长率
            for (int i = 1; i < teamData.size(); i++) {
                CommitStatsResponseDTO current = teamData.get(i);
                CommitStatsResponseDTO previous = teamData.get(i - 1);

                if (previous.getTotalLines() != null && previous.getTotalLines() > 0) {
                    long currentLines = current.getTotalLines();
                    long previousLines = previous.getTotalLines();

                    // 判断是否需要预估
                    boolean isLastRecord = (i == teamData.size() - 1);
                    boolean needsEstimation = false;
                    double estimationFactor = 1.0;

                    // 默认设置为非预估
                    current.setIsEstimated(false);

                    if (isLastRecord) {
                        String period = current.getPeriod();
                        if (period != null) {
                            if (period.contains("Q")) {
                                // 季度数据预估
                                needsEstimation = isIncompleteQuarterData(period, currentYear, completeMonth);
                                if (needsEstimation) {
                                    estimationFactor = getQuarterEstimationFactor(period, currentYear, completeMonth);
                                }
                            } else if (period.matches("\\d{4}")) {
                                // 年度数据预估
                                int dataYear = Integer.parseInt(period);
                                if (dataYear == currentYear && completeMonth < 12 && completeMonth > 0) {
                                    needsEstimation = true;
                                    estimationFactor = 12.0 / completeMonth;
                                }
                            }
                        }
                    }

                    // 如果需要预估，调整当前数据
                    if (needsEstimation) {
                        currentLines = Math.round(currentLines * estimationFactor);
                        current.setIsEstimated(true);
                    }

                    // 计算增长率
                    double growthRate = ((currentLines - previousLines) * 100.0) / previousLines;
                    current.setGrowthRate(Math.round(growthRate * 100.0) / 100.0);
                } else {
                    // 如果前一期数据为0或null，设置增长率为0
                    current.setGrowthRate(0.0);
                }
            }
        });
    }
    
    /**
     * 计算同比增长率（季度数据与去年同期对比）
     * <AUTHOR>
     * @date 2025-08-21 11:24:05
     */
    private void calculateYearOverYearGrowthRate(List<CommitStatsResponseDTO> dataList) {
        if (dataList.isEmpty()) {
            return;
        }
        
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        int currentMonth = currentDate.getMonthValue();
        int completeMonth = currentMonth - 1;
        
        // 按时间周期排序
        dataList.sort((a, b) -> {
            String periodA = a.getPeriod();
            String periodB = b.getPeriod();
            return periodA.compareTo(periodB);
        });
        
        // 为每个季度数据计算同比增长率
        for (int i = 0; i < dataList.size(); i++) {
            CommitStatsResponseDTO current = dataList.get(i);
            String currentPeriod = current.getPeriod();
            if (currentPeriod == null || !currentPeriod.contains("Q")) {
                continue; // 只处理季度数据
            }
            
            // 解析当前季度信息
            try {
                String[] parts = currentPeriod.split("-Q");
                if (parts.length == 2) {
                    int dataYear = Integer.parseInt(parts[0]);
                    int quarter = Integer.parseInt(parts[1]);
                    
                    // 计算去年同期的期间标识
                    String lastYearSamePeriod = (dataYear - 1) + "-Q" + quarter;
                    
                    // 查找去年同期的数据
                    CommitStatsResponseDTO lastYearData = dataList.stream()
                            .filter(item -> lastYearSamePeriod.equals(item.getPeriod()))
                            .findFirst()
                            .orElse(null);
                    
                    if (lastYearData != null && lastYearData.getTotalLines() != null && lastYearData.getTotalLines() > 0) {
                        long currentLines = current.getTotalLines();
                        long lastYearLines = lastYearData.getTotalLines();
                        
                        // 判断是否是最后一条数据且可能是不完整的数据
                        boolean isLastRecord = (i == dataList.size() - 1);
                        boolean needsEstimation = false;
                        double estimationFactor = 1.0;
                        
                        if (isLastRecord && dataYear == currentYear) {
                            // 判断是否需要预估计算
                            needsEstimation = isIncompleteQuarterData(currentPeriod, currentYear, completeMonth);
                            if (needsEstimation) {
                                estimationFactor = getQuarterEstimationFactor(currentPeriod, currentYear, completeMonth);
                            }
                        }
                        
                        // 如果需要预估，调整当前数据
                        if (needsEstimation) {
                            currentLines = Math.round(currentLines * estimationFactor);
                        }
                        
                        // 计算同比增长率
                        double yearOverYearGrowthRate = ((currentLines - lastYearLines) * 100.0) / lastYearLines;
                        current.setYearOverYearGrowthRate(Math.round(yearOverYearGrowthRate * 100.0) / 100.0);
                    } else {
                        // 没有去年同期数据，设置为null
                        current.setYearOverYearGrowthRate(null);
                    }
                }
            } catch (NumberFormatException e) {
                // 解析失败，设置为null
                current.setYearOverYearGrowthRate(null);
            }
        }
    }
    
    /**
     * 计算半年度同比增长率（半年度数据与去年同期对比）
     * <AUTHOR>
     * @date 2025-08-21 19:35:10
     */
    private void calculateHalfYearOverYearGrowthRate(List<CommitStatsResponseDTO> dataList) {
        if (dataList.isEmpty()) {
            return;
        }
        
        LocalDate currentDate = LocalDate.now();
        int currentYear = currentDate.getYear();
        int currentMonth = currentDate.getMonthValue();
        int completeMonth = currentMonth - 1;
        
        // 按时间周期排序
        dataList.sort((a, b) -> {
            String periodA = a.getPeriod();
            String periodB = b.getPeriod();
            return periodA.compareTo(periodB);
        });
        
        // 为每个半年度数据计算同比增长率
        for (int i = 0; i < dataList.size(); i++) {
            CommitStatsResponseDTO current = dataList.get(i);
            String currentPeriod = current.getPeriod();
            if (currentPeriod == null || !currentPeriod.contains("H")) {
                continue; // 只处理半年度数据
            }
            
            // 解析当前半年度信息
            try {
                String[] parts = currentPeriod.split("-H");
                if (parts.length == 2) {
                    int dataYear = Integer.parseInt(parts[0]);
                    int halfYear = Integer.parseInt(parts[1]);
                    
                    // 计算去年同期的期间标识
                    String lastYearSamePeriod = (dataYear - 1) + "-H" + halfYear;
                    
                    // 查找去年同期的数据
                    CommitStatsResponseDTO lastYearData = dataList.stream()
                            .filter(item -> lastYearSamePeriod.equals(item.getPeriod()))
                            .findFirst()
                            .orElse(null);
                    
                    if (lastYearData != null && lastYearData.getTotalLines() != null && lastYearData.getTotalLines() > 0) {
                        long currentLines = current.getTotalLines();
                        long lastYearLines = lastYearData.getTotalLines();
                        
                        // 判断当前半年度数据是否需要预估（不只是最后一条记录）
                        boolean needsEstimation = false;
                        double estimationFactor = 1.0;
                        
                        if (dataYear == currentYear) {
                            // 判断是否需要预估计算
                            needsEstimation = isIncompleteHalfYearData(currentPeriod, currentYear, completeMonth);
                            if (needsEstimation) {
                                estimationFactor = getHalfYearEstimationFactor(currentPeriod, currentYear, completeMonth);
                            }
                        }
                        
                        // 如果需要预估，调整当前数据
                        if (needsEstimation) {
                            currentLines = Math.round(currentLines * estimationFactor);
                            current.setIsEstimated(true);
                        }
                        
                        // 计算同比增长率（使用预估后的数据）
                        double yoyGrowthRate = ((currentLines - lastYearLines) * 100.0) / lastYearLines;
                        current.setYearOverYearGrowthRate(Math.round(yoyGrowthRate * 100.0) / 100.0);
                    } else {
                        // 没有去年同期数据，设置为null
                        current.setYearOverYearGrowthRate(null);
                    }
                }
            } catch (NumberFormatException e) {
                // 解析失败，设置为null
                current.setYearOverYearGrowthRate(null);
            }
        }
    }
    
    /**
     * 判断半年度数据是否不完整
     * <AUTHOR>
     * @date 2025-08-21 19:35:10
     */
    private boolean isIncompleteHalfYearData(String period, int currentYear, int completeMonth) {
        if (!period.contains("H") || !period.startsWith(String.valueOf(currentYear))) {
            return false;
        }
        
        String halfYearStr = period.substring(period.indexOf("H") + 1);
        try {
            int halfYear = Integer.parseInt(halfYearStr);
            
            // 判断当前是哪个半年度
            int currentHalfYear = completeMonth <= 6 ? 1 : 2;
            
            // 只有当前进行中的半年度才需要预估，已结束的半年度不需要预估
            if (halfYear != currentHalfYear) {
                return false;
            }
            
            // 判断当前半年度是否已结束
            int halfYearEndMonth = halfYear == 1 ? 6 : 12;
            return completeMonth < halfYearEndMonth;
        } catch (NumberFormatException e) {
            return false;
        }
    }
    
    /**
     * 计算半年度数据的预估因子
     * 公式：预估因子 = 6 / (当前半年已过月份 - 1)
     * 例如：当前8月，已过月份7月，下半年预估因子 = 6 / (1) = 6
     * <AUTHOR>
     * @date 2025-08-21 19:35:10
     */
    private double getHalfYearEstimationFactor(String period, int currentYear, int completeMonth) {
        String halfYearStr = period.substring(period.indexOf("H") + 1);
        try {
            int halfYear = Integer.parseInt(halfYearStr);
            int halfYearStartMonth = halfYear == 1 ? 1 : 7;
            
            // 判断当前是哪个半年度
            int currentHalfYear = completeMonth <= 6 ? 1 : 2;
            
            // 只有当前进行中的半年度才需要预估
            if (halfYear != currentHalfYear) {
                return 1.0;
            }
            
            // 计算当前半年度已过的月份数（实际有数据的月份）
            // 例如：当前8月，实际数据到7月
            // 下半年从7月开始，已过月份数 = 7 - 7 + 1 = 1个月
            int passedMonths = completeMonth - halfYearStartMonth + 1;
            
            if (passedMonths <= 0) {
                return 1.0;
            }
            
            // 使用公式：预估因子 = 6 / 已过月份数
            return 6.0 / passedMonths;
        } catch (NumberFormatException e) {
            return 1.0;
        }
    }
}