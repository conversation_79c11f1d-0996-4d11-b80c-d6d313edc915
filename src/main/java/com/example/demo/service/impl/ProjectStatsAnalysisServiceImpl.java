package com.example.demo.service.impl;

import com.alibaba.excel.EasyExcel;
import com.example.demo.mapper.primary.ProjectStatsAnalysisMapper;
import com.example.demo.service.ProjectStatsAnalysisService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 项目统计分析服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
@Service
public class ProjectStatsAnalysisServiceImpl implements ProjectStatsAnalysisService {

    private static final Logger logger = LoggerFactory.getLogger(ProjectStatsAnalysisServiceImpl.class);

    @Autowired
    private ProjectStatsAnalysisMapper statsMapper;

    @Override
    public List<String> findDistinctProjectNames() {
        logger.debug("查询所有项目名列表");
        return statsMapper.findDistinctProjectNames();
    }

    @Override
    public List<String> findRecentProjectsByOnlineDate() {
        logger.debug("查询最近半年内上线的项目列表");
        return statsMapper.findRecentProjectsByOnlineDate();
    }

    @Override
    public Map<String, Map<String, Integer>> getProjectComparisonStats(List<String> projectNames, 
                                                                       String grayDateStart, String grayDateEnd, 
                                                                       String onlineDateStart, String onlineDateEnd) {
        logger.info("获取项目对比统计，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = statsMapper.getProjectBasicStats(
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Map<String, Integer>> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String projectName = (String) item.get("project_name");
            if (StringUtils.hasText(projectName)) {
                Map<String, Integer> stats = new HashMap<>();
                stats.put("总问题数", ((Number) item.get("total_issues")).intValue());
                stats.put("开发人员数", ((Number) item.get("dev_count")).intValue());
                stats.put("测试人员数", ((Number) item.get("tester_count")).intValue());
                stats.put("验收人员数", ((Number) item.get("acceptor_count")).intValue());
                stats.put("平台数", ((Number) item.get("platform_count")).intValue());
                stats.put("模块数", ((Number) item.get("module_count")).intValue());
                result.put(projectName, stats);
            }
        }
        
        logger.debug("项目对比统计结果：{}", result);
        return result;
    }

    @Override
    public Map<String, Map<String, Object>> getProjectQualityStats(List<String> projectNames, 
                                                                   String grayDateStart, String grayDateEnd, 
                                                                   String onlineDateStart, String onlineDateEnd) {
        logger.info("获取项目质量指标统计，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = statsMapper.getProjectQualityIndicators(
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Map<String, Object>> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String projectName = (String) item.get("project_name");
            if (StringUtils.hasText(projectName)) {
                Map<String, Object> stats = new HashMap<>();
                stats.put("总问题数", ((Number) item.get("total_issues")).intValue());
                stats.put("开发问题数", ((Number) item.get("dev_issues")).intValue());
                stats.put("测试问题数", ((Number) item.get("test_issues")).intValue());
                stats.put("有测试用例数", ((Number) item.get("has_test_case_count")).intValue());
                stats.put("可验收数", ((Number) item.get("testable_count")).intValue());
                stats.put("已完成数", ((Number) item.get("completed_count")).intValue());
                stats.put("测试用例覆盖率", item.get("test_case_coverage"));
                stats.put("可验收率", item.get("testable_rate"));
                stats.put("完成率", item.get("completion_rate"));
                result.put(projectName, stats);
            }
        }
        
        logger.debug("项目质量指标统计结果：{}", result);
        return result;
    }

    @Override
    public Map<String, Map<String, Integer>> getProjectDevIssueComparison(List<String> projectNames, 
                                                                          String grayDateStart, String grayDateEnd, 
                                                                          String onlineDateStart, String onlineDateEnd) {
        logger.info("获取项目开发问题分类对比，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = statsMapper.getProjectDevIssueStats(
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Map<String, Integer>> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String projectName = (String) item.get("project_name");
            String category = (String) item.get("category");
            Integer count = ((Number) item.get("count")).intValue();
            
            if (StringUtils.hasText(projectName) && StringUtils.hasText(category)) {
                result.computeIfAbsent(projectName, k -> new HashMap<>()).put(category, count);
            }
        }
        
        logger.debug("项目开发问题分类对比结果：{}", result);
        return result;
    }

    @Override
    public Map<String, Map<String, Integer>> getProjectTestIssueComparison(List<String> projectNames, 
                                                                           String grayDateStart, String grayDateEnd, 
                                                                           String onlineDateStart, String onlineDateEnd) {
        logger.info("获取项目测试问题分类对比，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        List<Map<String, Object>> rawData = statsMapper.getProjectTestIssueStats(
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        Map<String, Map<String, Integer>> result = new HashMap<>();
        for (Map<String, Object> item : rawData) {
            String projectName = (String) item.get("project_name");
            String category = (String) item.get("category");
            Integer count = ((Number) item.get("count")).intValue();
            
            if (StringUtils.hasText(projectName) && StringUtils.hasText(category)) {
                result.computeIfAbsent(projectName, k -> new HashMap<>()).put(category, count);
            }
        }
        
        logger.debug("项目测试问题分类对比结果：{}", result);
        return result;
    }

    @Override
    public String exportProjectStatsToExcel(List<String> projectNames, 
                                            String grayDateStart, String grayDateEnd, 
                                            String onlineDateStart, String onlineDateEnd) throws Exception {
        logger.info("导出项目统计分析数据到Excel，参数：项目名={}, 灰度日期区间=[{}, {}], 上线日期区间=[{}, {}]", 
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        // 获取统计数据
        Map<String, Map<String, Integer>> comparisonStats = getProjectComparisonStats(
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        Map<String, Map<String, Object>> qualityStats = getProjectQualityStats(
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        Map<String, Map<String, Integer>> devIssueStats = getProjectDevIssueComparison(
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        Map<String, Map<String, Integer>> testIssueStats = getProjectTestIssueComparison(
                projectNames, grayDateStart, grayDateEnd, onlineDateStart, onlineDateEnd);
        
        // 准备导出数据
        List<Map<String, Object>> exportData = new ArrayList<>();
        
        // 合并所有项目的统计数据
        Set<String> allProjects = new HashSet<>();
        allProjects.addAll(comparisonStats.keySet());
        allProjects.addAll(qualityStats.keySet());
        
        for (String projectName : allProjects) {
            Map<String, Object> row = new HashMap<>();
            row.put("项目名称", projectName);
            
            // 基础统计数据
            Map<String, Integer> comparison = comparisonStats.get(projectName);
            if (comparison != null) {
                row.put("总问题数", comparison.get("总问题数"));
                row.put("开发人员数", comparison.get("开发人员数"));
                row.put("测试人员数", comparison.get("测试人员数"));
                row.put("验收人员数", comparison.get("验收人员数"));
                row.put("平台数", comparison.get("平台数"));
                row.put("模块数", comparison.get("模块数"));
            }
            
            // 质量指标数据
            Map<String, Object> quality = qualityStats.get(projectName);
            if (quality != null) {
                row.put("开发问题数", quality.get("开发问题数"));
                row.put("测试问题数", quality.get("测试问题数"));
                row.put("测试用例覆盖率", quality.get("测试用例覆盖率"));
                row.put("可验收率", quality.get("可验收率"));
                row.put("完成率", quality.get("完成率"));
            }
            
            exportData.add(row);
        }
        
        // 生成文件名
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        String fileName = "项目统计分析_" + timestamp + ".xlsx";
        String filePath = System.getProperty("java.io.tmpdir") + File.separator + fileName;
        
        // 导出Excel
        EasyExcel.write(filePath)
                .head(createExcelHeaders())
                .sheet("项目统计分析")
                .doWrite(exportData);
        
        logger.info("项目统计分析数据导出成功，文件路径：{}", filePath);
        return filePath;
    }
    
    /**
     * 创建Excel表头
     */
    private List<List<String>> createExcelHeaders() {
        List<List<String>> headers = new ArrayList<>();
        headers.add(Arrays.asList("项目名称"));
        headers.add(Arrays.asList("总问题数"));
        headers.add(Arrays.asList("开发人员数"));
        headers.add(Arrays.asList("测试人员数"));
        headers.add(Arrays.asList("验收人员数"));
        headers.add(Arrays.asList("平台数"));
        headers.add(Arrays.asList("模块数"));
        headers.add(Arrays.asList("开发问题数"));
        headers.add(Arrays.asList("测试问题数"));
        headers.add(Arrays.asList("测试用例覆盖率"));
        headers.add(Arrays.asList("可验收率"));
        headers.add(Arrays.asList("完成率"));
        return headers;
    }
}
