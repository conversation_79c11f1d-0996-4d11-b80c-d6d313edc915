package com.example.demo.service.impl;

import com.example.demo.entity.secondary.TableSyncConfig;
import com.example.demo.mapper.secondary.TableSyncConfigMapper;
import com.example.demo.service.TableSyncConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 表同步配置服务实现类
 *
 * <AUTHOR>
 * @date 2024-05-30 07:55:39
 */
@Service
public class TableSyncConfigServiceImpl implements TableSyncConfigService {

    @Autowired
    private TableSyncConfigMapper tableSyncConfigMapper;

    /**
     * 查询有效的表同步配置
     *
     * @param sourceDb 源库名称
     * @param sourceTable 源表名称
     * @param targetTable 目标表名称
     * @param syncType 接入方式
     * @return 表同步配置列表
     */
    @Override
    public List<TableSyncConfig> queryValidConfigs(String sourceDb, String sourceTable, String targetTable, String syncType) {
        // 首先获取所有有效的配置
        List<TableSyncConfig> allConfigs = tableSyncConfigMapper.selectAllValid();
        
        // 如果没有查询条件，直接返回所有有效配置
        if (!StringUtils.hasText(sourceDb) && !StringUtils.hasText(sourceTable) 
                && !StringUtils.hasText(targetTable) && !StringUtils.hasText(syncType)) {
            return allConfigs;
        }
        
        // 根据条件过滤
        return allConfigs.stream()
                .filter(config -> {
                    // 源库名称匹配
                    if (StringUtils.hasText(sourceDb) && !sourceDb.equals(config.getSourceDb())) {
                        return false;
                    }
                    
                    // 源表名称模糊匹配
                    if (StringUtils.hasText(sourceTable) 
                            && !config.getSourceTable().toLowerCase().contains(sourceTable.toLowerCase())) {
                        return false;
                    }
                    
                    // 目标表名称模糊匹配
                    if (StringUtils.hasText(targetTable) 
                            && !config.getTargetTable().toLowerCase().contains(targetTable.toLowerCase())) {
                        return false;
                    }
                    
                    // 接入方式匹配
                    if (StringUtils.hasText(syncType) && !syncType.equals(config.getSyncType())) {
                        return false;
                    }
                    
                    return true;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取所有源库名称
     *
     * @return 源库名称列表
     */
    @Override
    public List<String> getSourceDbs() {
        List<TableSyncConfig> allConfigs = tableSyncConfigMapper.selectAllValid();
        Set<String> uniqueSourceDbs = new HashSet<>();
        
        // 收集所有不同的源库名称
        for (TableSyncConfig config : allConfigs) {
            if (StringUtils.hasText(config.getSourceDb())) {
                uniqueSourceDbs.add(config.getSourceDb());
            }
        }
        
        return new ArrayList<>(uniqueSourceDbs);
    }

    /**
     * 获取所有接入方式
     *
     * @return 接入方式列表
     */
    @Override
    public List<String> getSyncTypes() {
        List<TableSyncConfig> allConfigs = tableSyncConfigMapper.selectAllValid();
        Set<String> uniqueSyncTypes = new HashSet<>();
        
        // 收集所有不同的接入方式
        for (TableSyncConfig config : allConfigs) {
            if (StringUtils.hasText(config.getSyncType())) {
                uniqueSyncTypes.add(config.getSyncType());
            }
        }
        
        return new ArrayList<>(uniqueSyncTypes);
    }
    
    /**
     * 根据ID获取表同步配置
     *
     * @param id 配置ID
     * @return 表同步配置信息
     */
    @Override
    public TableSyncConfig getConfigById(Long id) {
        return tableSyncConfigMapper.selectById(id);
    }
    
    /**
     * 新增表同步配置
     *
     * @param config 表同步配置信息
     * @return 新增的配置ID
     */
    @Override
    public Long addConfig(TableSyncConfig config) {
        // 设置状态为有效
        config.setStatus(1);
        
        // 插入配置并返回生成的ID
        tableSyncConfigMapper.insert(config);
        return config.getId();
    }
    
    /**
     * 更新表同步配置
     *
     * @param config 表同步配置信息
     * @return 影响的行数
     */
    @Override
    public int updateConfig(TableSyncConfig config) {
        return tableSyncConfigMapper.update(config);
    }
    
    /**
     * 逻辑删除表同步配置
     *
     * @param id 配置ID
     * @return 是否删除成功
     */
    @Override
    public boolean logicalDeleteConfig(Long id) {
        // 先查询配置是否存在且有效
        TableSyncConfig config = tableSyncConfigMapper.selectById(id);
        
        if (config == null || config.getStatus() == 0) {
            return false;
        }
        
        // 设置状态为无效(0)
        config.setStatus(0);
        
        // 更新状态
        int rows = tableSyncConfigMapper.update(config);
        
        return rows > 0;
    }
} 