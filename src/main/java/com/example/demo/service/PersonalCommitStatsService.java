package com.example.demo.service;

import com.example.demo.dto.PersonalCommitStatsRequestDTO;
import com.example.demo.dto.PersonalCommitStatsResponseDTO;

import java.util.List;

/**
 * 个人代码提交统计服务接口
 * <AUTHOR>
 * @date 2025-08-23 10:15:55
 */
public interface PersonalCommitStatsService {
    
    /**
     * 获取个人统计数据（根据维度类型）
     */
    List<PersonalCommitStatsResponseDTO> getPersonalStatsData(PersonalCommitStatsRequestDTO request);
    
    /**
     * 获取个人年度统计数据
     */
    List<PersonalCommitStatsResponseDTO> getPersonalYearlyStats(PersonalCommitStatsRequestDTO request);
    
    /**
     * 获取个人半年度统计数据
     */
    List<PersonalCommitStatsResponseDTO> getPersonalHalfYearlyStats(PersonalCommitStatsRequestDTO request);
    
    /**
     * 获取个人季度统计数据
     */
    List<PersonalCommitStatsResponseDTO> getPersonalQuarterlyStats(PersonalCommitStatsRequestDTO request);
    
    /**
     * 获取Top N开发者排名
     */
    List<PersonalCommitStatsResponseDTO> getTopDevelopersRanking(PersonalCommitStatsRequestDTO request);
    
    /**
     * 获取所有开发者用户名
     */
    List<String> getAllDevelopers();
    
    /**
     * 获取指定团队的开发者用户名
     */
    List<String> getDevelopersByTeams(List<String> teams);
}