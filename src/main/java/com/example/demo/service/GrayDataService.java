package com.example.demo.service;

import com.example.demo.entity.primary.GrayData;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 灰度数据服务接口
 *
 * <AUTHOR>
 * @date 2025-07-09 16:54:24
 */
public interface GrayDataService {
    
    /**
     * 根据条件查询灰度数据
     *
     * @param projectName 项目名（支持模糊查询）
     * @param acceptanceEnv 验收环境
     * @return 灰度数据列表
     */
    List<GrayData> findByConditions(String projectName, String acceptanceEnv);
    
    /**
     * 获取所有项目名列表（去重）
     *
     * @return 项目名列表
     */
    List<String> findDistinctProjectNames();
    
    /**
     * 获取所有验收环境列表（去重）
     *
     * @return 验收环境列表
     */
    List<String> findDistinctAcceptanceEnvs();
    
    /**
     * 导入Excel文件
     *
     * @param file Excel文件
     * @return 导入结果信息
     * @throws Exception 导入异常
     */
    String importExcel(MultipartFile file) throws Exception;

    /**
     * 导入Excel文件（支持覆盖）
     *
     * @param file Excel文件
     * @param forceOverwrite 是否强制覆盖现有数据
     * @return 导入结果信息
     * @throws Exception 导入异常
     */
    String importExcel(MultipartFile file, boolean forceOverwrite) throws Exception;

    /**
     * 导入Excel文件（带日期参数）
     *
     * @param file Excel文件
     * @param grayDate 灰度日期
     * @param onlineDate 上线日期
     * @return 导入结果信息
     * @throws Exception 导入异常
     */
    String importExcel(MultipartFile file, String grayDate, String onlineDate) throws Exception;

    /**
     * 导入Excel文件（支持覆盖，带日期参数）
     *
     * @param file Excel文件
     * @param forceOverwrite 是否强制覆盖
     * @param grayDate 灰度日期
     * @param onlineDate 上线日期
     * @return 导入结果信息
     * @throws Exception 导入异常
     */
    String importExcel(MultipartFile file, boolean forceOverwrite, String grayDate, String onlineDate) throws Exception;
    
    /**
     * 检查项目名是否已存在数据
     *
     * @param projectName 项目名
     * @return 是否存在
     */
    boolean existsByProjectName(String projectName);
}
