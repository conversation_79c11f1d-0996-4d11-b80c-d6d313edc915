package com.example.demo.service;

import java.util.List;
import java.util.Map;

/**
 * 项目统计分析服务接口
 *
 * <AUTHOR>
 * @date 2025-07-10 19:30:35
 */
public interface ProjectStatsAnalysisService {

    /**
     * 获取所有项目名列表（去重）
     *
     * @return 项目名列表
     */
    List<String> findDistinctProjectNames();

    /**
     * 获取最近半年内上线的项目列表
     *
     * @return 最近半年内上线的项目名列表
     */
    List<String> findRecentProjectsByOnlineDate();

    /**
     * 获取项目对比统计数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目对比统计数据 Map<项目名, Map<统计类型, 数量>>
     */
    Map<String, Map<String, Integer>> getProjectComparisonStats(List<String> projectNames, 
                                                                String grayDateStart, String grayDateEnd, 
                                                                String onlineDateStart, String onlineDateEnd);

    /**
     * 获取项目质量指标统计数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目质量指标统计数据 Map<项目名, Map<指标名, 指标值>>
     */
    Map<String, Map<String, Object>> getProjectQualityStats(List<String> projectNames, 
                                                            String grayDateStart, String grayDateEnd, 
                                                            String onlineDateStart, String onlineDateEnd);

    /**
     * 获取项目开发问题分类对比数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目开发问题分类对比数据 Map<项目名, Map<问题分类, 数量>>
     */
    Map<String, Map<String, Integer>> getProjectDevIssueComparison(List<String> projectNames, 
                                                                   String grayDateStart, String grayDateEnd, 
                                                                   String onlineDateStart, String onlineDateEnd);

    /**
     * 获取项目测试问题分类对比数据
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return 项目测试问题分类对比数据 Map<项目名, Map<问题分类, 数量>>
     */
    Map<String, Map<String, Integer>> getProjectTestIssueComparison(List<String> projectNames, 
                                                                    String grayDateStart, String grayDateEnd, 
                                                                    String onlineDateStart, String onlineDateEnd);

    /**
     * 导出项目统计分析数据到Excel
     *
     * @param projectNames 项目名称列表
     * @param grayDateStart 灰度日期开始
     * @param grayDateEnd 灰度日期结束
     * @param onlineDateStart 上线日期开始
     * @param onlineDateEnd 上线日期结束
     * @return Excel文件路径
     * @throws Exception 导出异常
     */
    String exportProjectStatsToExcel(List<String> projectNames, 
                                     String grayDateStart, String grayDateEnd, 
                                     String onlineDateStart, String onlineDateEnd) throws Exception;
}
