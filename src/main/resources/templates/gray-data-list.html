<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(~{::title}, ~{::head}, ~{::content})}">
<head>
    <title>项目验收数据</title>

    <!-- 立即执行的脚本 - 定义全局函数 -->
    <script>
        // 全局函数定义，确保在HTML解析时就可用
        window.testImportClick = function() {
            console.log('全局testImportClick函数被调用');

            // 检查导入对话框函数是否已加载
            if (typeof window.openImportDialog === 'function') {
                console.log('导入对话框函数已加载，直接调用');
                window.openImportDialog();
            } else {
                console.log('导入对话框函数还未加载，等待加载完成...');
                // 显示加载提示
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        title: '正在加载...',
                        text: '导入功能正在初始化，请稍候',
                        allowOutsideClick: false,
                        showConfirmButton: false,
                        didOpen: () => {
                            Swal.showLoading();
                        }
                    });
                }

                // 等待页面初始化完成后再调用
                var checkImportFunction = function(attempts) {
                    attempts = attempts || 0;
                    if (typeof window.openImportDialog === 'function') {
                        console.log('导入对话框函数已加载，开始调用');
                        if (typeof Swal !== 'undefined') {
                            Swal.close();
                        }
                        window.openImportDialog();
                    } else if (attempts < 50) { // 最多等待5秒
                        setTimeout(function() {
                            checkImportFunction(attempts + 1);
                        }, 100);
                    } else {
                        console.error('导入功能加载超时');
                        if (typeof Swal !== 'undefined') {
                            Swal.close();
                        }
                        alert('导入功能加载失败，请刷新页面重试');
                    }
                };
                checkImportFunction();
            }
        };

        /**
         * 打开导入对话框 - 早期定义确保可用性
         */
        window.openImportDialog = function() {
            console.log('openImportDialog 函数被调用');

            // 检查 SweetAlert2 是否可用
            if (typeof Swal === 'undefined') {
                console.error('SweetAlert2 未加载');
                alert('导入功能暂时不可用，请刷新页面重试');
                return;
            }

            console.log('开始显示导入对话框');
            Swal.fire({
                title: 'Excel文件导入',
                width: '500px',
                html: `
                    <div style="text-align: left; margin: 20px 0; padding: 0 10px;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">选择Excel文件：</label>
                            <input type="file" id="importFileInput" accept=".xlsx,.xls" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">灰度日期：</label>
                            <input type="date" id="grayDateInput" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; min-width: 0;">
                        </div>
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: 500;">上线日期：</label>
                            <input type="date" id="onlineDateInput" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; min-width: 0;">
                        </div>
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 14px; color: #6c757d;">
                            <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
                            说明：项目名称将自动取Excel文件名，灰度日期和上线日期为可选项
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '开始导入',
                cancelButtonText: '取消',
                confirmButtonColor: '#28a745',
                cancelButtonColor: '#6c757d',
                preConfirm: () => {
                    const fileInput = document.getElementById('importFileInput');
                    const grayDateInput = document.getElementById('grayDateInput');
                    const onlineDateInput = document.getElementById('onlineDateInput');
                    const file = fileInput.files[0];

                    if (!file) {
                        Swal.showValidationMessage('请选择要导入的Excel文件');
                        return false;
                    }

                    if (!window.isValidExcelFile(file)) {
                        Swal.showValidationMessage('请选择有效的Excel文件（.xlsx 或 .xls）');
                        return false;
                    }

                    // 格式化日期为yyyyMMdd格式
                    const formatDate = (dateStr) => {
                        if (!dateStr) return '';
                        return dateStr.replace(/-/g, '');
                    };

                    return {
                        file: file,
                        grayDate: formatDate(grayDateInput.value),
                        onlineDate: formatDate(onlineDateInput.value)
                    };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    window.handleFileImport(result.value.file, false, result.value.grayDate, result.value.onlineDate);
                }
            });
        };

        /**
         * 检查是否为有效的Excel文件
         */
        window.isValidExcelFile = function(file) {
            const validTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];
            const validExtensions = ['.xlsx', '.xls'];

            return validTypes.includes(file.type) ||
                   validExtensions.some(ext => file.name.toLowerCase().endsWith(ext));
        };

        /**
         * 处理文件导入
         */
        window.handleFileImport = function(file, forceOverwrite, grayDate, onlineDate) {
            console.log('开始导入文件:', file.name, '强制覆盖:', forceOverwrite, '灰度日期:', grayDate, '上线日期:', onlineDate);

            // 显示加载状态
            window.showLoading();

            // 创建FormData
            const formData = new FormData();
            formData.append('file', file);
            if (forceOverwrite) {
                formData.append('forceOverwrite', 'true');
            }
            if (grayDate) {
                formData.append('grayDate', grayDate);
            }
            if (onlineDate) {
                formData.append('onlineDate', onlineDate);
            }

            // 选择合适的URL
            const url = forceOverwrite ? '/gray-data/import-with-overwrite' : '/gray-data/import';

            // 发送AJAX请求
            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                timeout: 60000, // 60秒超时
                success: function(response) {
                    console.log('导入响应:', response);
                    window.handleImportResponse(response, file, grayDate, onlineDate);
                },
                error: function(xhr, status, error) {
                    console.error('导入失败:', xhr, status, error);
                    window.handleImportError(xhr);
                },
                complete: function() {
                    window.hideLoading();
                }
            });
        };

        /**
         * 显示加载状态
         */
        window.showLoading = function() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'flex';
            }
        };

        /**
         * 隐藏加载状态
         */
        window.hideLoading = function() {
            const loadingOverlay = document.getElementById('loadingOverlay');
            if (loadingOverlay) {
                loadingOverlay.style.display = 'none';
            }
        };

        /**
         * 处理导入响应
         */
        window.handleImportResponse = function(response, file, grayDate, onlineDate) {
            if (response.success) {
                // 导入成功
                window.showSuccessAlert(response.message).then(() => {
                    // 成功后刷新页面
                    window.location.reload();
                });
            } else if (response.duplicate) {
                // 项目重复，需要用户确认
                window.showDuplicateConfirmDialog(response, file, grayDate, onlineDate);
            } else {
                // 其他错误
                window.showErrorAlert(response.message || '导入失败，请重试');
            }
        };

        /**
         * 显示重复导入确认对话框
         */
        window.showDuplicateConfirmDialog = function(response, file, grayDate, onlineDate) {
            const projectName = response.projectName || '未知项目';
            const existingCount = response.existingRecordCount || 0;

            Swal.fire({
                icon: 'warning',
                title: '项目数据已存在',
                html: `
                    <div style="text-align: left; margin: 20px 0;">
                        <p style="margin-bottom: 15px; font-size: 16px;">
                            <strong>项目名：</strong>${projectName}
                        </p>
                        <p style="margin-bottom: 15px; font-size: 16px;">
                            <strong>现有数据：</strong>${existingCount} 条记录
                        </p>
                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; padding: 15px; margin: 15px 0;">
                            <i class="fas fa-exclamation-triangle" style="color: #856404; margin-right: 8px;"></i>
                            <span style="color: #856404; font-weight: 500;">
                                确认覆盖将删除现有的 ${existingCount} 条数据，并导入新的数据。此操作不可撤销！
                            </span>
                        </div>
                        <p style="margin-top: 15px; font-size: 14px; color: #6c757d;">
                            请选择您要执行的操作：
                        </p>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: '确认覆盖',
                cancelButtonText: '取消导入',
                confirmButtonColor: '#dc3545',
                cancelButtonColor: '#6c757d',
                reverseButtons: true,
                focusCancel: true
            }).then((result) => {
                if (result.isConfirmed) {
                    // 用户确认覆盖，重新导入
                    console.log('用户确认覆盖，开始强制导入');
                    window.handleFileImport(file, true, grayDate, onlineDate);
                } else {
                    console.log('用户取消覆盖导入');
                }
            });
        };

        /**
         * 处理导入错误
         */
        window.handleImportError = function(xhr) {
            let errorMessage = '导入失败，请稍后重试';

            if (xhr.status === 413) {
                errorMessage = '文件太大，请选择较小的文件';
            } else if (xhr.status === 415) {
                errorMessage = '不支持的文件格式，请选择Excel文件';
            } else if (xhr.responseJSON && xhr.responseJSON.message) {
                errorMessage = xhr.responseJSON.message;
            } else if (xhr.status === 0) {
                errorMessage = '网络连接失败，请检查网络后重试';
            }

            window.showErrorAlert(errorMessage);
        };

        /**
         * 显示成功提示
         */
        window.showSuccessAlert = function(message) {
            return Swal.fire({
                icon: 'success',
                title: '导入成功',
                text: message,
                confirmButtonText: '确定',
                confirmButtonColor: '#28a745',
                timer: 3000,
                timerProgressBar: true
            });
        };

        /**
         * 显示错误提示
         */
        window.showErrorAlert = function(message) {
            return Swal.fire({
                icon: 'error',
                title: '导入失败',
                text: message,
                confirmButtonText: '确定',
                confirmButtonColor: '#dc3545'
            });
        };

        console.log('全局testImportClick函数已定义');
    </script>

    <style>
        /* 页面整体样式 */
        .page-container {
            padding: 20px;
            background-color: #f8f9fa;
            min-height: 100vh;
        }





        /* 查询区域样式 */
        .filter-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .filter-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .filter-header i {
            font-size: 20px;
            color: #007bff;
            margin-right: 10px;
        }

        .filter-header h4 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }

        .filter-form {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .filter-group {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .filter-group label {
            font-weight: 500;
            color: #495057;
            white-space: nowrap;
        }

        .filter-group select {
            min-width: 180px;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 6px;
            font-size: 14px;
            background-color: white;
        }

        .filter-actions {
            display: flex;
            gap: 10px;
        }

        .btn-filter {
            padding: 8px 20px !important;
            border: none !important;
            border-radius: 6px !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            text-decoration: none !important;
            display: inline-block !important;
            vertical-align: middle !important;
        }

        .btn-filter.primary {
            background-color: #007bff !important;
            color: white !important;
        }

        .btn-filter.primary:hover {
            background-color: #0056b3 !important;
            color: white !important;
        }

        .btn-filter.secondary {
            background-color: #6c757d !important;
            color: white !important;
        }

        .btn-filter.secondary:hover {
            background-color: #545b62 !important;
            color: white !important;
        }

        .btn-filter.import {
            background-color: #28a745 !important;
            color: white !important;
        }

        .btn-filter.import:hover {
            background-color: #218838 !important;
            color: white !important;
        }

        /* 确保导入按钮不受base.html样式影响 */
        #importBtn {
            background-color: #28a745 !important;
            color: white !important;
            border: none !important;
            padding: 8px 20px !important;
            border-radius: 6px !important;
            cursor: pointer !important;
            font-size: 14px !important;
            font-weight: 500 !important;
        }

        #importBtn:hover {
            background-color: #218838 !important;
            color: white !important;
        }

        /* 数据表格样式 */
        .data-section {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: 1px solid #e9ecef;
        }

        .data-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .data-title {
            display: flex;
            align-items: center;
        }

        .data-title i {
            font-size: 20px;
            color: #6f42c1;
            margin-right: 10px;
        }

        .data-title h4 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }

        .data-count {
            background-color: #f8f9fa;
            color: #6c757d;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 14px;
            font-weight: 500;
        }

        .table-wrapper {
            border-radius: 8px;
            overflow: hidden;
            border: 1px solid #dee2e6;
        }

        .table-container {
            max-height: 600px;
            overflow-y: auto;
            overflow-x: auto;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            font-size: 13px;
        }

        .data-table th {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            color: #495057;
            font-weight: 600;
            padding: 12px 8px;
            text-align: left;
            border-bottom: 2px solid #dee2e6;
            position: sticky;
            top: 0;
            z-index: 10;
            white-space: nowrap;
        }

        .data-table td {
            padding: 10px 8px;
            border-bottom: 1px solid #f1f3f4;
            vertical-align: top;
        }

        .data-table tr:hover {
            background-color: #f8f9fa;
        }

        .description-cell {
            max-width: 200px;
            word-wrap: break-word;
            white-space: pre-wrap;
            line-height: 1.4;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 64px;
            color: #dee2e6;
            margin-bottom: 20px;
        }

        .empty-state h5 {
            margin-bottom: 10px;
            color: #6c757d;
        }

        .empty-state p {
            margin: 0;
            font-size: 14px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .page-container {
                padding: 10px;
            }

            .filter-form {
                flex-direction: column;
                align-items: stretch;
            }

            .filter-group {
                flex-direction: column;
                align-items: stretch;
                gap: 5px;
            }

            .filter-group select {
                min-width: auto;
            }

            .data-table {
                font-size: 12px;
            }

            .data-table th,
            .data-table td {
                padding: 8px 4px;
            }
        }

        /* 加载动画 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            display: none;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 5px solid #f3f3f3;
            border-top: 5px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }


    </style>
</head>
<body>
    <div th:fragment="content">
        <!-- 加载遮罩 -->
        <div class="loading-overlay" id="loadingOverlay">
            <div class="loading-spinner"></div>
        </div>

        <div class="page-container">
            <!-- 查询条件 -->
            <div class="filter-section">
                <div class="filter-header">
                    <i class="fas fa-search"></i>
                    <h4>数据筛选</h4>
                </div>

                <form method="get" th:action="@{/gray-data/list}" class="filter-form">
                    <div class="filter-group">
                        <label for="projectName">项目名：</label>
                        <select id="projectName" name="projectName" class="select2-single">
                            <option value="">请选择项目</option>
                            <option th:each="name : ${projectNames}"
                                    th:value="${name}"
                                    th:text="${name}"
                                    th:selected="${name == selectedProjectName}"></option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="acceptanceEnv">验收环境：</label>
                        <select id="acceptanceEnv" name="acceptanceEnv">
                            <option value="">请选择环境</option>
                            <option th:each="env : ${acceptanceEnvs}"
                                    th:value="${env}"
                                    th:text="${env}"
                                    th:selected="${env == selectedAcceptanceEnv}"></option>
                        </select>
                    </div>

                    <div class="filter-actions">
                        <button type="submit" class="btn-filter primary">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <button type="button" class="btn-filter secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> 清空
                        </button>
                        <button type="button" class="btn-filter import" id="importBtn" onclick="testImportClick()">
                            <i class="fas fa-upload"></i> 导入
                        </button>
                    </div>
                </form>
            </div>

            <!-- 数据表格 -->
            <div class="data-section">
                <div class="data-header">
                    <div class="data-title">
                        <i class="fas fa-table"></i>
                        <h4>数据列表</h4>
                    </div>
                    <div class="data-count" th:if="${grayDataList != null}">
                        共 <span th:text="${#lists.size(grayDataList)}">0</span> 条记录
                    </div>
                </div>

                <div class="table-wrapper">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>项目名</th>
                                    <th>验收环境</th>
                                    <th>平台</th>
                                    <th>问题类型</th>
                                    <th>模块</th>
                                    <th>页面</th>
                                    <th>问题描述</th>
                                    <th>修改进度</th>
                                    <th>当前是否可验</th>
                                    <th>验收人</th>
                                    <th>问题归因</th>
                                    <th>开发对接人</th>
                                    <th>开发备注</th>
                                    <th>开发问题分类</th>
                                    <th>是否有测试用例</th>
                                    <th>测试问题分类</th>
                                    <th>测试原因分析</th>
                                    <th>测试人员</th>
                                    <th>灰度日期</th>
                                    <th>上线日期</th>
                                    <th>创建时间</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr th:if="${#lists.isEmpty(grayDataList)}">
                                    <td colspan="21">
                                        <div class="empty-state">
                                            <i class="fas fa-inbox"></i>
                                            <h5>暂无数据</h5>
                                            <p>请上传Excel文件或调整筛选条件</p>
                                        </div>
                                    </td>
                                </tr>
                                <tr th:each="data : ${grayDataList}">
                                    <td th:text="${data.projectName}"></td>
                                    <td th:text="${data.acceptanceEnv}"></td>
                                    <td th:text="${data.platform}"></td>
                                    <td th:text="${data.issueType}"></td>
                                    <td th:text="${data.module}"></td>
                                    <td th:text="${data.page}"></td>
                                    <td class="description-cell" th:text="${data.issueDescription}"></td>
                                    <td th:text="${data.modifyProgress}"></td>
                                    <td th:text="${data.currentTestable}"></td>
                                    <td th:text="${data.acceptor}"></td>
                                    <td th:text="${data.issueAttribution}"></td>
                                    <td th:text="${data.devContact}"></td>
                                    <td class="description-cell" th:text="${data.devRemark}"></td>
                                    <td th:text="${data.devIssueCategory}"></td>
                                    <td th:text="${data.hasTestCase}"></td>
                                    <td th:text="${data.testIssueCategory}"></td>
                                    <td class="description-cell" th:text="${data.testReasonAnalysis}"></td>
                                    <td th:text="${data.tester}"></td>
                                    <td th:text="${data.grayDate}"></td>
                                    <td th:text="${data.onlineDate}"></td>
                                    <td th:text="${#dates.format(data.createTime, 'yyyy-MM-dd HH:mm')}"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        /**
         * 灰度数据页面JavaScript - 导入功能
         * <AUTHOR>
         * @date 2025-07-09 20:08:50
         */

        // 确保在页面完全加载后执行
        function initializePage() {
            console.log('页面加载完成，开始初始化');

            // 延迟执行，确保所有资源都已加载
            setTimeout(function() {
                initializeComponents();
                console.log('页面初始化完成');
            }, 200);
        }

        // 使用window.onload确保所有资源都已加载
        if (window.addEventListener) {
            window.addEventListener('load', initializePage, false);
        } else if (window.attachEvent) {
            window.attachEvent('onload', initializePage);
        } else {
            window.onload = initializePage;
        }

        // 备用方案：使用DOMContentLoaded
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(initializePage, 300);
        });

        /**
         * 初始化页面组件
         */
        function initializeComponents() {
            try {
                console.log('开始初始化组件');

                // 等待jQuery加载
                var checkJQuery = function() {
                    if (typeof $ !== 'undefined' && typeof jQuery !== 'undefined') {
                        console.log('jQuery 已加载');
                        initializeSelect2();
                        initializeImportButton();
                    } else {
                        console.log('等待jQuery加载...');
                        setTimeout(checkJQuery, 100);
                    }
                };
                checkJQuery();

            } catch (error) {
                console.error('组件初始化失败:', error);
            }
        }

        /**
         * 初始化Select2组件
         */
        function initializeSelect2() {
            try {
                // 检查 Select2 是否可用
                if (typeof $.fn.select2 === 'undefined') {
                    console.warn('Select2 未加载，跳过初始化');
                    return;
                }

                // 初始化Select2
                $('#projectName').select2({
                    placeholder: '请选择或输入项目名',
                    allowClear: true,
                    tags: true,
                    width: '100%'
                });
                console.log('Select2 初始化完成');
            } catch (error) {
                console.error('Select2 初始化失败:', error);
            }
        }

        /**
         * 初始化导入按钮
         */
        function initializeImportButton() {
            try {
                console.log('开始初始化导入按钮');

                // 先移除所有现有的事件监听器
                $('#importBtn').off('click');

                const importBtn = document.getElementById('importBtn');
                if (!importBtn) {
                    console.error('未找到导入按钮元素');
                    return;
                }

                // 使用原生JavaScript绑定事件，避免jQuery冲突
                importBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log('导入按钮被点击');
                    window.openImportDialog();
                }, true);

                console.log('导入按钮事件绑定完成');
            } catch (error) {
                console.error('导入按钮初始化失败:', error);
            }
        }







        /**
         * 清空筛选条件
         */
        function clearFilters() {
            $('#projectName').val('').trigger('change');
            $('#acceptanceEnv').val('');
            window.location.href = '/gray-data/list';
        }

        /**
         * 调试函数：检查页面状态
         */
        function debugPageStatus() {
            console.log('=== 页面状态调试信息 ===');
            console.log('jQuery 版本:', typeof $ !== 'undefined' ? $.fn.jquery : '未加载');
            console.log('SweetAlert2:', typeof Swal !== 'undefined' ? '已加载' : '未加载');
            console.log('Select2:', typeof $.fn.select2 !== 'undefined' ? '已加载' : '未加载');

            const importBtn = document.getElementById('importBtn');
            console.log('导入按钮元素:', importBtn ? '找到' : '未找到');

            if (importBtn) {
                console.log('按钮样式:', window.getComputedStyle(importBtn));
                console.log('按钮事件监听器数量:', importBtn.getEventListeners ? importBtn.getEventListeners('click').length : '无法检测');
            }
            console.log('========================');
        }

        // 添加全局调试函数
        window.debugPageStatus = debugPageStatus;


    </script>
</body>
</html>
