<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:fragment="html(title, head, content)">
<head>
    <meta charset="UTF-8">
    <title th:replace="${title}">默认标题</title>
    <!-- 通用CSS和JS -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <!-- SweetAlert2 -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.min.css">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11.7.32/dist/sweetalert2.all.min.js"></script>
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <style>
        /* 导航菜单样式 */
        .nav-menu {
            background-color: #333;
            padding: 10px 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav-menu ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            gap: 20px;
        }
        .nav-menu li {
            display: inline;
        }
        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: background-color 0.3s;
        }
        .nav-menu a:hover {
            background-color: #555;
        }
        .nav-menu a.active {
            background-color: #4CAF50;
        }

        /* 下拉菜单样式 */
        .nav-menu .dropdown {
            position: relative;
            display: inline-block;
        }

        .nav-menu .dropdown-content {
            display: none;
            position: absolute;
            background-color: #444;
            min-width: 160px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1000;
            border-radius: 3px;
            top: 100%;
            left: 0;
        }

        .nav-menu .dropdown-content a {
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            display: block;
            border-radius: 0;
        }

        .nav-menu .dropdown-content a:hover {
            background-color: #555;
        }

        .nav-menu .dropdown:hover .dropdown-content {
            display: block;
        }

        .nav-menu .dropdown > a::after {
            content: ' ▼';
            font-size: 10px;
            margin-left: 5px;
        }
        
        /* 退出按钮样式 */
        .logout-btn {
            background-color: #dc3545;
            color: white;
            padding: 5px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
        .logout-btn:hover {
            background-color: #c82333;
        }
        
        /* 通用样式 */
        .filter-container {
            margin: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .filter-container .select2 {
            width: 200px !important;
            margin-right: 10px;
        }
        .filter-container button {
            padding: 8px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            vertical-align: top;
            margin-top: 1px;
        }
        .filter-container button:hover {
            background-color: #45a049;
        }
        .select2-container--default .select2-selection--multiple {
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border-color: #4CAF50;
        }
        .filter-item {
            display: inline-block;
            margin-right: 20px;
        }
        .filter-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="month"] {
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 150px;
        }
    </style>
    <!-- 页面特定的头部内容 -->
    <th:block th:replace="${head}" />
</head>
<body>
    <!-- 导航菜单 -->
    <nav class="nav-menu">
        <ul>
            <li class="dropdown">
                <a href="#" th:class="${active == 'commits' || active == 'commits-weekly' || active == 'commit-stats' || active == 'personal-commit-stats' ? 'active' : ''}">代码统计</a>
                <div class="dropdown-content">
                    <a th:href="@{/commits/chart}" th:class="${active == 'commits' ? 'active' : ''}">代码提交统计</a>
                    <a th:href="@{/commits-weekly/chart}" th:class="${active == 'commits-weekly' ? 'active' : ''}">代码按周统计</a>
                    <a th:href="@{/commit-stats}" th:class="${active == 'commit-stats' ? 'active' : ''}">历年代码量统计</a>
                    <a th:href="@{/personal-commit-stats}" th:class="${active == 'personal-commit-stats' ? 'active' : ''}">历年代码量统计（个人）</a>
                </div>
            </li>
            <li><a th:href="@{/bugs/chart}" th:class="${active == 'bugs' ? 'active' : ''}">Bug率统计</a></li>
            <li class="dropdown">
                <a href="#" th:class="${active == 'worktime' || active == 'worktime-stats' ? 'active' : ''}">工时统计</a>
                <div class="dropdown-content">
                    <a th:href="@{/worktime/chart}" th:class="${active == 'worktime' ? 'active' : ''}">工时分布统计</a>
                    <a th:href="@{/worktime-stats/chart}" th:class="${active == 'worktime-stats' ? 'active' : ''}">工时统计</a>
                </div>
            </li>
            <li><a th:href="@{/product-line/bugs}" th:class="${active == 'product-line' ? 'active' : ''}">产线Bug统计</a></li>
            <li><a th:href="@{/test-execution/stats}" th:class="${active == 'test-execution' ? 'active' : ''}">自动化执行统计</a></li>
            <li class="dropdown">
                <a href="#" th:class="${active == 'project-acceptance' ? 'active' : ''}">项目验收数据</a>
                <div class="dropdown-content">
                    <a th:href="@{/gray-data/list}" th:class="${active == 'gray-data-detail' ? 'active' : ''}">明细数据</a>
                    <a th:href="@{/project-acceptance/analysis}" th:class="${active == 'project-acceptance-analysis' ? 'active' : ''}">单项目数据分析</a>
                    <a th:href="@{/project-stats/analysis}" th:class="${active == 'project-stats-analysis' ? 'active' : ''}">项目统计分析</a>
                    <a th:href="@{/project-stats-data/list}" th:class="${active == 'project-stats-data' ? 'active' : ''}">项目统计数据</a>
                </div>
            </li>
        </ul>
        <form th:action="@{/logout}" method="post" style="margin: 0;">
            <button type="submit" class="logout-btn">退出登录</button>
        </form>
    </nav>
    
    <!-- 页面主体内容 -->
    <div th:replace="${content}">
        页面内容
    </div>
</body>
</html> 