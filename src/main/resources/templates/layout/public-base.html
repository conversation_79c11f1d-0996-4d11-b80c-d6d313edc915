<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:fragment="html(title, head, content)">
<head>
    <meta charset="UTF-8">
    <title th:replace="${title}">默认标题</title>
    <!-- 通用CSS和JS -->
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <style>
        /* 导航菜单样式 */
        .nav-menu {
            background-color: #333;
            padding: 10px 20px;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .nav-menu ul {
            list-style: none;
            margin: 0;
            padding: 0;
            display: flex;
            gap: 20px;
        }
        .nav-menu li {
            display: inline;
        }
        .nav-menu a {
            color: white;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: background-color 0.3s;
        }
        .nav-menu a:hover {
            background-color: #555;
        }
        .nav-menu a.active {
            background-color: #4CAF50;
        }
        
        /* 登录按钮样式 */
        .login-btn {
            background-color: #4a90e2;
            color: white;
            padding: 5px 15px;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            text-decoration: none;
            font-size: 14px;
        }
        .login-btn:hover {
            background-color: #357abd;
        }
        
        /* 通用样式 */
        .filter-container {
            margin: 20px;
            padding: 15px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .filter-container .select2 {
            width: 200px !important;
            margin-right: 10px;
        }
        .filter-container button {
            padding: 8px 20px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
            vertical-align: top;
            margin-top: 1px;
        }
        .filter-container button:hover {
            background-color: #45a049;
        }
        .select2-container--default .select2-selection--multiple {
            border-radius: 3px;
            border: 1px solid #ddd;
        }
        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border-color: #4CAF50;
        }
        .filter-item {
            display: inline-block;
            margin-right: 20px;
        }
        .filter-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        input[type="month"] {
            padding: 6px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 150px;
        }
    </style>
    <!-- 页面特定的头部内容 -->
    <th:block th:replace="${head}" />
</head>
<body>
    <!-- 公共导航菜单 - 无需登录 -->
    <nav class="nav-menu">
        <ul>
            <li><a th:href="@{/table-sync/configs}" th:class="${publicActive == 'table-sync' ? 'active' : ''}">数据仓库表同步配置</a></li>
            <!-- 此处可以添加其他无需登录的菜单 -->
        </ul>
        <div>

        </div>
    </nav>
    
    <!-- 页面主体内容 -->
    <div th:replace="${content}">
        页面内容
    </div>
</body>
</html> 