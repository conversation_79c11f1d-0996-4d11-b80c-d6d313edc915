<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::content}
      )}">
<head>
    <title>研发千行bug率统计</title>
    <style>
        .charts-container {
            display: flex;
            flex-direction: column;
            gap: 20px;
            padding: 0 20px;
        }
        .chart {
            width: 100%;
            height: 400px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div th:fragment="content">
        <div class="filter-container">
            <form th:action="@{/bugs/chart}" method="get" id="filterForm">
                <div class="filter-item">
                    <label class="filter-label">团队：</label>
                    <select name="selectedTeam" class="select2-single">
                        <option value="">全部团队</option>
                        <option th:each="team : ${teams}" 
                                th:value="${team}" 
                                th:text="${team}"
                                th:selected="${team == selectedTeam}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">用户名：</label>
                    <select name="selectedUsers" multiple class="select2-multi" data-placeholder="选择用户...">
                        <option th:each="username : ${usernames}" 
                                th:value="${username}" 
                                th:text="${username}"
                                th:selected="${selectedUsers != null && selectedUsers.contains(username)}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">起始月份：</label>
                    <input type="month" name="startMonth" th:value="${startMonth}">
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">结束月份：</label>
                    <input type="month" name="endMonth" th:value="${endMonth}">
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">统计维度：</label>
                    <select name="selectedMode" class="select2-single">
                        <option value="">全部</option>
                        <option value="one_year" th:selected="${selectedMode == 'one_year'}">一年</option>
                        <option value="half_year" th:selected="${selectedMode == 'half_year' || selectedMode == null}">半年</option>
                        <option value="quarter_year" th:selected="${selectedMode == 'quarter_year'}">季度</option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">&nbsp;</label>
                    <button type="submit">筛选</button>
                    <button type="button" onclick="clearSelection()">重置</button>
                </div>
            </form>
        </div>
        
        <div class="charts-container">
            <div id="bugRateChart" class="chart"></div>
            <div id="bugTotalChart" class="chart"></div>
            <div id="codeTotalChart" class="chart"></div>
        </div>

        <script th:inline="javascript">
            // 初始化Select2
            $(document).ready(function() {
                $('.select2-multi').select2({
                    allowClear: true,
                    closeOnSelect: false,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的用户";
                        }
                    }
                });
                
                $('.select2-single').select2({
                    allowClear: true,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的团队";
                        }
                    }
                });

                // 监听团队选择变化
                $('select[name="selectedTeam"]').on('change', function() {
                    var selectedTeam = $(this).val();
                    var userSelect = $('.select2-multi');
                    
                    if (selectedTeam) {
                        $.get('/bugs/users-by-team', { team: selectedTeam }, function(users) {
                            var currentSelected = userSelect.val() || [];
                            userSelect.empty();
                            users.forEach(function(username) {
                                var newOption = new Option(username, username, false, false);
                                userSelect.append(newOption);
                            });
                            // 恢复之前选中的用户（如果在新的用户列表中存在）
                            var validSelected = currentSelected.filter(user => users.includes(user));
                            userSelect.val(validSelected).trigger('change');
                            // 自动提交表单
                            document.getElementById('filterForm').submit();
                        });
                    } else {
                        var allUsers = /*[[${usernames}]]*/ [];
                        var currentSelected = userSelect.val() || [];
                        userSelect.empty();
                        allUsers.forEach(function(username) {
                            var newOption = new Option(username, username, false, false);
                            userSelect.append(newOption);
                        });
                        // 恢复之前选中的用户
                        userSelect.val(currentSelected).trigger('change');
                        // 自动提交表单
                        document.getElementById('filterForm').submit();
                    }
                });

                // 监听用户选择变化
                $('.select2-multi').on('change', function() {
                    document.getElementById('filterForm').submit();
                });

                // 监听月份变化
                $('input[type="month"]').on('change', function() {
                    document.getElementById('filterForm').submit();
                });

                // 监听周期选择变化
                $('select[name="selectedMode"]').on('change', function() {
                    document.getElementById('filterForm').submit();
                });
            });

            // 修改清除选择函数
            function clearSelection() {
                $('.select2-single').val(null).trigger('change');
                $('.select2-multi').val(null).trigger('change');
                $('input[type="month"]').val('');
                $('select[name="selectedMode"]').val('').trigger('change');
                document.getElementById('filterForm').submit();
            }

            // 获取后端传递的数据
            var bugs = /*[[${bugs}]]*/ [];
            var months = /*[[${months}]]*/ [];
            var selectedUsers = /*[[${selectedUsers}]]*/ [];
            var startMonth = /*[[${startMonth}]]*/ '';
            var endMonth = /*[[${endMonth}]]*/ '';

            // 处理数据
            var usernames = [...new Set(bugs.map(item => item.fixer))];
            if (selectedUsers && selectedUsers.length > 0) {
                usernames = selectedUsers;
            }

            // 过滤月份范围
            months = months.filter(function(month) {
                return (!startMonth || month >= startMonth) && (!endMonth || month <= endMonth);
            });

            // 准备三个图表的数据
            function prepareSeriesData(dataKey) {
                return usernames.map(function(username) {
                    var data = months.map(function(month) {
                        var bug = bugs.find(function(b) {
                            return b.fixer === username && b.untilDay === month;
                        });
                        return bug ? bug[dataKey] : 0;
                    });
                    
                    return {
                        name: username,
                        type: 'line',
                        data: data,
                        smooth: true
                    };
                });
            }

            // 创建图表的通用配置
            function createChartOption(title, yAxisName, series, formatter) {
                return {
                    title: {
                        text: title
                    },
                    tooltip: {
                        trigger: 'axis',
                        formatter: formatter
                    },
                    legend: {
                        data: usernames,
                        type: 'scroll',
                        orient: 'vertical',
                        right: 10,
                        top: 20,
                        bottom: 20
                    },
                    grid: {
                        left: '3%',
                        right: '15%',
                        bottom: '3%',
                        containLabel: true
                    },
                    xAxis: {
                        type: 'category',
                        data: months,
                        axisLabel: {
                            rotate: 45,
                            formatter: function(value) {
                                // 如果日期格式包含日，则去掉日期部分
                                if (value && value.length > 7) {
                                    return value.substring(0, 7);
                                }
                                return value;
                            }
                        }
                    },
                    yAxis: {
                        type: 'value',
                        name: yAxisName
                    },
                    series: series
                };
            }

            // 初始化三个图表
            var bugRateChart = echarts.init(document.getElementById('bugRateChart'));
            var bugTotalChart = echarts.init(document.getElementById('bugTotalChart'));
            var codeTotalChart = echarts.init(document.getElementById('codeTotalChart'));

            // Bug率图表
            var bugRateSeries = prepareSeriesData('bugs');
            var bugRateOption = createChartOption(
                '研发人员千行bug率统计',
                '千行bug率(‰)',
                bugRateSeries,
                function(params) {
                    var result = params[0].axisValue.substring(0, 7) + '<br/>'; // 去掉日期部分
                    params.forEach(function(param) {
                        result += param.marker + param.seriesName + ': ' + 
                                 (param.value || 0).toFixed(2) + '‰<br/>';
                    });
                    return result;
                }
            );

            // Bug总数图表
            var bugTotalSeries = prepareSeriesData('bugTotal');
            var bugTotalOption = createChartOption(
                '研发人员Bug总数统计',
                'Bug总数',
                bugTotalSeries,
                function(params) {
                    var result = params[0].axisValue.substring(0, 7) + '<br/>'; // 去掉日期部分
                    params.forEach(function(param) {
                        result += param.marker + param.seriesName + ': ' + 
                                 param.value + ' 个<br/>';
                    });
                    return result;
                }
            );

            // 代码总行数图表
            var codeTotalSeries = prepareSeriesData('codeTotal');
            var codeTotalOption = createChartOption(
                '研发人员代码行数统计',
                '代码行数',
                codeTotalSeries,
                function(params) {
                    var result = params[0].axisValue.substring(0, 7) + '<br/>'; // 去掉日期部分
                    params.forEach(function(param) {
                        result += param.marker + param.seriesName + ': ' + 
                                 param.value + ' 行<br/>';
                    });
                    return result;
                }
            );

            // 设置图表配置
            bugRateChart.setOption(bugRateOption);
            bugTotalChart.setOption(bugTotalOption);
            codeTotalChart.setOption(codeTotalOption);

            // 响应式调整
            window.addEventListener('resize', function() {
                bugRateChart.resize();
                bugTotalChart.resize();
                codeTotalChart.resize();
            });
        </script>
    </div>
</body>
</html> 