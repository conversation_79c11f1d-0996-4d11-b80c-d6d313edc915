<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::content}
      )}">
<head>
    <title>历年代码提交量统计报表（按人）</title>
    <style>
        /* 年份选择器不换行样式 */
        .year-select-no-wrap .select2-selection--multiple {
            white-space: nowrap;
            overflow: hidden;
            max-width: 200px;
            overflow-x: auto;
            overflow-y: hidden;
        }
        
        .year-select-no-wrap .select2-selection__rendered {
            white-space: nowrap;
            overflow: hidden;
            display: flex;
            flex-wrap: nowrap;
        }
        
        .year-select-no-wrap .select2-selection__choice {
            flex-shrink: 0;
            margin-right: 3px;
            font-size: 12px;
            padding: 2px 6px;
        }
        
        /* Top排名控件区域样式统一 */
        #topYearSelectDiv .year-select-no-wrap .select2-selection--multiple {
            max-width: 150px;
        }
        
        #topYearSelectDiv select,
        #topLimitDiv select {
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div th:fragment="content">
        <h2>历年代码提交量统计报表（按人）</h2>
        <p style="color: #666;">统计个人历年代码提交情况，支持个人排名和增长率分析</p>
        
        <!-- 筛选条件区域 -->
        <div style="margin-bottom: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label style="font-size: 14px; color: #333; white-space: nowrap;">统计维度：</label>
                    <select id="dimensionType" style="padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        <option value="YEAR">按年度统计</option>
                        <option value="HALF_YEAR" selected>按半年度统计</option>
                        <option value="QUARTER">按季度统计</option>
                        <option value="TOP_RANKING">Top排名</option>
                    </select>
                </div>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label style="font-size: 14px; color: #333; white-space: nowrap;">团队选择：</label>
                    <select id="teamSelect" multiple style="min-width: 200px; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;" data-placeholder="选择团队（可多选）">
                        <option th:each="team : ${allTeams}" 
                                th:value="${team}" 
                                th:text="${team}"></option>
                    </select>
                </div>
                <div style="display: flex; align-items: center; gap: 10px;" id="developerSelectDiv">
                    <label style="font-size: 14px; color: #333; white-space: nowrap;">开发者：</label>
                    <select id="developerSelect" multiple style="min-width: 150px; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;" data-placeholder="选择开发者（可多选）">
                        <option th:each="developer : ${allDevelopers}" 
                                th:value="${developer}" 
                                th:text="${developer}"></option>
                    </select>
                </div>
                <div id="yearSelectDiv" style="display: flex; align-items: center; gap: 10px;">
                    <label style="font-size: 14px; color: #333; white-space: nowrap;">年份选择：</label>
                    <div class="year-select-no-wrap">
                        <select id="yearSelect" multiple style="min-width: 150px; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;" data-placeholder="选择年份（可多选）">
                            <option th:each="year : ${availableYears}" 
                                    th:value="${year}" 
                                    th:text="${year}"></option>
                        </select>
                    </div>
                </div>
                <div id="topYearSelectDiv" style="display: none; align-items: center; gap: 10px;">
                    <label style="font-size: 14px; color: #333; white-space: nowrap;">年份选择：</label>
                    <div class="year-select-no-wrap">
                        <select id="topYearSelect" multiple style="min-width: 120px; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;" data-placeholder="选择年份（可多选）">
                            <option th:each="year : ${availableYears}" 
                                    th:value="${year}" 
                                    th:text="${year}"></option>
                        </select>
                    </div>
                </div>
                <div id="topLimitDiv" style="display: none; align-items: center; gap: 10px;">
                    <label style="font-size: 14px; color: #333; white-space: nowrap;">Top数量：</label>
                    <select id="topLimit" style="padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        <option value="10" selected>前10名</option>
                        <option value="20">前20名</option>
                        <option value="50">前50名</option>
                    </select>
                </div>
                <div style="display: flex; gap: 10px; margin-left: auto;">
                    <button type="button" onclick="loadPersonalStatsData()" style="padding: 6px 20px; background: #0d6efd; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                        查询
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 数据表格区域 -->
        <div style="margin: 20px 0;">
            <h3>详细数据</h3>
            <div style="margin-top: 10px;">
                <table id="personalStatsTable" style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
                    <thead style="background-color: #343a40; color: white;">
                        <tr id="personalTableHeader">
                            <th style="padding: 10px; border: 1px solid #ddd;">时间周期</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">开发者</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">团队</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">代码行数</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">月均行数</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">增长率</th>
                        </tr>
                    </thead>
                    <tbody id="personalTableBody">
                        <tr>
                            <td colspan="6" style="padding: 20px; text-align: center; color: #666; border: 1px solid #ddd;">请选择条件后点击查询按钮</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 图表展示区域 -->
        <div style="margin: 20px 0;">
            <h3>趋势图表</h3>
            <div id="personalChartContainer" style="height: 400px; width: 100%; border: 1px solid #ddd; border-radius: 5px; margin-top: 10px;">
                <div style="text-align: center; padding: 50px; color: #666;">
                    图表加载中...
                </div>
            </div>
        </div>

    <script th:inline="javascript">
    /*<![CDATA[*/
        // 页面初始化
        $(document).ready(function() {
            // 初始化Select2组件
            $('#teamSelect').select2({
                placeholder: '选择团队（可多选）',
                allowClear: true,
                width: 'resolve'
            });
            
            $('#developerSelect').select2({
                placeholder: '选择开发者（可多选）',
                allowClear: true,
                width: 'resolve'
            });
            
            $('#topYearSelect').select2({
                placeholder: '选择年份（可多选）',
                allowClear: true,
                width: 'resolve'
            });
            
            $('#yearSelect').select2({
                placeholder: '选择年份（可多选）',
                allowClear: true,
                width: 'resolve'
            });
            
            // 初始化图表
            initPersonalChart();
            
            // 监听统计维度变化
            $('#dimensionType').change(function() {
                toggleRankingControls();
            });
            
            // 监听团队选择变化，动态加载开发者列表
            $('#teamSelect').on('change', function() {
                loadDevelopersByTeams();
            });
            
            // 初始化控件显示状态
            toggleRankingControls();
            
            // 加载默认数据（默认查询全部年份）
            loadPersonalStatsData();
            
        });


        let personalStatsChart;

        // 初始化ECharts图表
        function initPersonalChart() {
            personalStatsChart = echarts.init(document.getElementById('personalChartContainer'));
            
            // 设置初始加载状态
            personalStatsChart.showLoading({
                text: '数据加载中...',
                color: '#0d6efd',
                maskColor: 'rgba(255, 255, 255, 0.8)'
            });
        }

        // 切换排名控件显示状态
        function toggleRankingControls() {
            const dimensionType = $('#dimensionType').val();
            const topYearSelectDiv = $('#topYearSelectDiv');
            const topLimitDiv = $('#topLimitDiv');
            const developerSelectDiv = $('#developerSelectDiv');
            const yearSelectDiv = $('#yearSelectDiv');
            
            if (dimensionType === 'TOP_RANKING') {
                topYearSelectDiv.show();
                topLimitDiv.show();
                developerSelectDiv.hide();
                yearSelectDiv.hide(); // Top排名使用自己的年份选择器
            } else {
                topYearSelectDiv.hide();
                topLimitDiv.hide();
                developerSelectDiv.show();
                yearSelectDiv.show(); // 其他维度显示通用年份选择器
            }
        }

        // 根据团队加载开发者列表
        function loadDevelopersByTeams() {
            const teams = $('#teamSelect').val();
            
            $.ajax({
                url: '/personal-commit-stats/developers',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(teams),
                success: function(developers) {
                    const $developerSelect = $('#developerSelect');
                    $developerSelect.empty();
                    
                    developers.forEach(function(developer) {
                        $developerSelect.append(new Option(developer, developer));
                    });
                    
                    // 重新初始化select2
                    $developerSelect.trigger('change');
                },
                error: function(xhr, status, error) {
                    console.error('加载开发者列表失败:', error);
                }
            });
        }

        // 加载个人统计数据
        function loadPersonalStatsData() {
            const teams = $('#teamSelect').val();
            const developers = $('#developerSelect').val();
            const dimensionType = $('#dimensionType').val();
            const topLimit = $('#topLimit').val();
            const topYears = $('#topYearSelect').val();
            const commonYears = $('#yearSelect').val();
            
            let years = null;
            if (dimensionType === 'TOP_RANKING') {
                // Top排名模式使用专门的年份选择器
                years = topYears && topYears.length > 0 ? topYears.map(year => parseInt(year)) : null;
            } else {
                // 其他模式（年度、季度、半年度）使用通用年份选择器
                years = commonYears && commonYears.length > 0 ? commonYears.map(year => parseInt(year)) : null;
            }
            
            const request = {
                years: years,
                teams: teams && teams.length > 0 ? teams : null,
                usernames: developers && developers.length > 0 ? developers : null,
                dimensionType: dimensionType,
                topLimit: dimensionType === 'TOP_RANKING' ? parseInt(topLimit) : null,
                rankingType: dimensionType === 'TOP_RANKING' ? 'TOTAL_LINES' : null
            };
            
            // 显示图表加载状态
            if (personalStatsChart) {
                personalStatsChart.showLoading({
                    text: '数据加载中...',
                    color: '#0d6efd',
                    maskColor: 'rgba(255, 255, 255, 0.8)'
                });
            }
            
            // 发送AJAX请求
            $.ajax({
                url: '/personal-commit-stats/data',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(request),
                success: function(data) {
                    updatePersonalChart(data);
                    updatePersonalTable(data);
                },
                error: function(xhr, status, error) {
                    if (personalStatsChart) {
                        personalStatsChart.hideLoading();
                    }
                    showErrorMessage('数据加载失败: ' + error);
                    console.error('请求失败:', error);
                }
            });
        }

        // 更新个人图表
        function updatePersonalChart(data) {
            if (!personalStatsChart) return;
            
            const dimensionType = $('#dimensionType').val();
            
            let option;
            if (dimensionType === 'TOP_RANKING') {
                option = getPersonalRankingChartOption(data);
            } else {
                option = getPersonalTrendChartOption(data);
            }
            
            personalStatsChart.hideLoading();
            personalStatsChart.setOption(option, true);
        }

        // 获取个人趋势图表配置
        function getPersonalTrendChartOption(data) {
            const developers = [...new Set(data.map(item => item.username))];
            const periods = [...new Set(data.map(item => item.period))].sort();
            const dimensionType = $('#dimensionType').val();
            const commonYears = $('#yearSelect').val();
            
            const colors = ['#0d6efd', '#198754', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14', '#20c997', '#6c757d'];
            
            const series = developers.slice(0, 10).map((developer, index) => ({
                name: developer,
                type: 'line',
                data: periods.map(period => {
                    const item = data.find(d => d.username === developer && d.period === period);
                    return item ? item.totalLines : 0;
                }),
                itemStyle: {
                    color: colors[index % colors.length]
                },
                smooth: true
            }));
            
            // 动态生成标题
            let titleText = '个人代码提交量趋势图';
            if (commonYears && commonYears.length > 0) {
                if (commonYears.length === 1) {
                    titleText = `${commonYears[0]}年 个人代码提交量趋势图`;
                } else {
                    titleText = `${commonYears.join('、')}年 个人代码提交量趋势图`;
                }
            }
            
            return {
                title: {
                    text: titleText,
                    left: 'center',
                    textStyle: {
                        fontSize: 16
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += param.marker + param.seriesName + ': ' + formatNumber(param.value) + '<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: developers.slice(0, 10),
                    bottom: 0,
                    type: 'scroll'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: periods
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: function(value) {
                            return formatNumber(value);
                        }
                    }
                },
                series: series
            };
        }

        // 获取个人排名图表配置
        function getPersonalRankingChartOption(data) {
            const developers = data.map(item => item.username);
            const values = data.map(item => item.totalLines);
            const topYears = $('#topYearSelect').val();
            
            let titleText = 'Top开发者排名';
            if (topYears && topYears.length > 0) {
                if (topYears.length === 1) {
                    titleText = `${topYears[0]}年 Top开发者排名`;
                } else {
                    titleText = `${topYears.join('、')}年 Top开发者排名`;
                }
            }
            
            return {
                title: {
                    text: titleText,
                    left: 'center',
                    textStyle: {
                        fontSize: 16
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        const param = params[0];
                        return param.name + '<br/>' + 
                               param.marker + '代码行数: ' + formatNumber(param.value);
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: developers,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: function(value) {
                            return formatNumber(value);
                        }
                    }
                },
                series: [{
                    name: '代码行数',
                    type: 'bar',
                    data: values,
                    itemStyle: {
                        color: function(params) {
                            const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
                            return colors[params.dataIndex % colors.length];
                        }
                    }
                }]
            };
        }

        // 更新个人表格
        function updatePersonalTable(data) {
            const dimensionType = $('#dimensionType').val();
            
            // 生成表头
            let headerHtml = '';
            if (dimensionType === 'TOP_RANKING') {
                headerHtml += '<th style="padding: 10px; border: 1px solid #ddd;">排名</th><th style="padding: 10px; border: 1px solid #ddd;">开发者</th><th style="padding: 10px; border: 1px solid #ddd;">团队</th><th style="padding: 10px; border: 1px solid #ddd;">代码行数</th><th style="padding: 10px; border: 1px solid #ddd;">月均行数</th><th style="padding: 10px; border: 1px solid #ddd;">团队占比</th>';
            } else {
                headerHtml += '<th style="padding: 10px; border: 1px solid #ddd;">时间周期</th><th style="padding: 10px; border: 1px solid #ddd;">开发者</th><th style="padding: 10px; border: 1px solid #ddd;">团队</th><th style="padding: 10px; border: 1px solid #ddd;">代码行数</th><th style="padding: 10px; border: 1px solid #ddd;">月均行数</th><th style="padding: 10px; border: 1px solid #ddd;">增长率</th>';
                // 所有时间维度统计都添加同比增长率列
                if (dimensionType === 'YEAR' || dimensionType === 'QUARTER' || dimensionType === 'HALF_YEAR') {
                    headerHtml += '<th style="padding: 10px; border: 1px solid #ddd;">同比增长率</th>';
                }
            }
            $('#personalTableHeader').html(headerHtml);
            
            // 生成数据行
            let bodyHtml = '';
            if (data.length === 0) {
                let colSpan = dimensionType === 'TOP_RANKING' ? 6 : 6;
                // 如果是时间维度统计，增加同比增长率列
                if ((dimensionType === 'YEAR' || dimensionType === 'QUARTER' || dimensionType === 'HALF_YEAR') && dimensionType !== 'TOP_RANKING') {
                    colSpan = 7;
                }
                bodyHtml = `<tr><td colspan="${colSpan}" style="padding: 20px; text-align: center; color: #666; border: 1px solid #ddd;">暂无数据</td></tr>`;
            } else {
                data.forEach((item, index) => {
                    const rowStyle = index % 2 === 0 ? 'background-color: #f8f9fa;' : '';
                    bodyHtml += `<tr style="${rowStyle}">`;
                    
                    if (dimensionType === 'TOP_RANKING') {
                        // 排名模式
                        const rankBadge = index < 3 ? `<span style="background: ${index === 0 ? '#ffd700' : index === 1 ? '#c0c0c0' : '#cd7f32'}; color: white; padding: 2px 6px; border-radius: 50%; font-weight: bold;">${index + 1}</span>` : `<span style="color: #666; font-weight: bold;">${index + 1}</span>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${rankBadge}</td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd;"><span style="background: #6f42c1; color: white; padding: 2px 8px; border-radius: 3px;">${item.username}</span></td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd;"><span style="background: #17a2b8; color: white; padding: 2px 8px; border-radius: 3px;">${item.team || '未分配'}</span></td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatNumber(item.totalLines)}</td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatNumber(item.avgMonthlyLines || 0)}</td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${(item.teamPercentage || 0).toFixed(2)}%</td>`;
                    } else {
                        // 趋势模式
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd;">${item.period}</td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd;"><span style="background: #6f42c1; color: white; padding: 2px 8px; border-radius: 3px;">${item.username}</span></td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd;"><span style="background: #17a2b8; color: white; padding: 2px 8px; border-radius: 3px;">${item.team || '未分配'}</span></td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatNumber(item.totalLines)}</td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatNumber(item.avgMonthlyLines || 0)}</td>`;
                        
                        const growthRate = item.growthRate || 0;
                        const growthColor = growthRate > 0 ? '#dc3545' : (growthRate < 0 ? '#28a745' : '#6c757d');
                        const growthIcon = growthRate > 0 ? '↗' : (growthRate < 0 ? '↘' : '-');
                        const estimatedLabel = item.isEstimated ? '<span style="color: #a306f6; font-size: 12px; margin-left: 4px;">(预估)</span>' : '';
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; color: ${growthColor}; font-weight: bold;">${growthIcon} ${Math.abs(growthRate).toFixed(2)}%${estimatedLabel}</td>`;
                        
                        // 如果是时间维度统计，添加同比增长率列
                        if (dimensionType === 'YEAR' || dimensionType === 'QUARTER' || dimensionType === 'HALF_YEAR') {
                            const yearOverYearRate = item.yearOverYearGrowthRate;
                            if (yearOverYearRate !== null && yearOverYearRate !== undefined) {
                                const yoyColor = yearOverYearRate > 0 ? '#dc3545' : (yearOverYearRate < 0 ? '#28a745' : '#6c757d');
                                const yoyIcon = yearOverYearRate > 0 ? '↗' : (yearOverYearRate < 0 ? '↘' : '-');
                                const yoyEstimatedLabel = item.isEstimated ? '<span style="color: #a306f6; font-size: 12px; margin-left: 4px;">(预估)</span>' : '';
                                bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; color: ${yoyColor}; font-weight: bold;">${yoyIcon} ${Math.abs(yearOverYearRate).toFixed(2)}%${yoyEstimatedLabel}</td>`;
                            } else {
                                bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; color: #6c757d; text-align: center;">-</td>`;
                            }
                        }
                    }
                    
                    bodyHtml += '</tr>';
                });
            }
            $('#personalTableBody').html(bodyHtml);
        }

        // 工具函数
        function formatNumber(num) {
            if (!num) return '0';
            if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }

        function showErrorMessage(message) {
            if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                console.error(message);
            } else {
                alert(message);
            }
        }
    /*]]>*/
    </script>
    </div>
</body>
</html>