<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::content}
      )}">
<head>
    <title>代码按周统计</title>
    <style>
        .chart-container {
            padding: 20px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px;
        }
        #weeklyChart {
            width: 100%;
            height: 600px;
        }
        .table-container {
            padding: 20px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px;
            overflow-x: auto;
        }
        
        .increment-table {
            width: 100%;
            border-collapse: collapse;
            text-align: center;
        }
        
        .increment-table th, 
        .increment-table td {
            padding: 10px;
            border: 1px solid #ddd;
            color: #333;
        }
        
        .increment-table th {
            background-color: #f5f5f5;
        }
        
        .arrow-up {
            color: #ff4d4d;
            margin-left: 4px;
            font-family: "Segoe UI Symbol", "Apple Color Emoji", sans-serif;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(255, 77, 77, 0.4);
            display: inline-block;
            vertical-align: middle;
        }
        
        .arrow-down {
            color: #2eb82e;
            margin-left: 4px;
            font-family: "Segoe UI Symbol", "Apple Color Emoji", sans-serif;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(46, 184, 46, 0.4);
            display: inline-block;
            vertical-align: middle;
        }
    </style>
</head>
<body>
    <div th:fragment="content">
        <div class="filter-container">
            <form th:action="@{/commits-weekly/chart}" method="get" id="filterForm">
                <div class="filter-item">
                    <label class="filter-label">团队：</label>
                    <select name="selectedTeam" class="select2-single">
                        <option value="">全部团队</option>
                        <option th:each="team : ${teams}" 
                                th:value="${team}" 
                                th:text="${team}"
                                th:selected="${team == selectedTeam}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">用户名：</label>
                    <select name="selectedUsers" multiple class="select2-multi" data-placeholder="选择用户...">
                        <option th:each="username : ${usernames}" 
                                th:value="${username}" 
                                th:text="${username}"
                                th:selected="${selectedUsers != null && selectedUsers.contains(username)}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">选择周：</label>
                    <select name="selectedWeek" class="select2-single">
                        <option value="">最新一周</option>
                        <option th:each="week : ${weeks}" 
                                th:value="${week}" 
                                th:text="${week}"
                                th:selected="${week == selectedWeek}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">&nbsp;</label>
                    <button type="submit">筛选</button>
                    <button type="button" onclick="clearSelection()">重置</button>
                </div>
            </form>
        </div>

        <div class="chart-container">
            <div id="weeklyChart"></div>
        </div>

        <div class="table-container">
            <table class="increment-table">
                <thead>
                    <tr>
                        <th>用户名</th>
                        <th th:each="week : ${recentWeeks}" th:text="${week}">周</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>

        <script th:inline="javascript">
            // 初始化Select2
            $(document).ready(function() {
                $('.select2-multi').select2({
                    allowClear: true,
                    closeOnSelect: false,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的用户";
                        }
                    }
                });
                
                $('.select2-single').select2({
                    allowClear: true,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的选项";
                        }
                    }
                });

                // 监听团队选择变化
                $('select[name="selectedTeam"]').on('change', function() {
                    var selectedTeam = $(this).val();
                    var userSelect = $('.select2-multi');
                    
                    if (selectedTeam) {
                        $.get('/commits-weekly/users-by-team', { team: selectedTeam }, function(users) {
                            var currentSelected = userSelect.val() || [];
                            userSelect.empty();
                            users.forEach(function(username) {
                                var newOption = new Option(username, username, false, false);
                                userSelect.append(newOption);
                            });
                            // 恢复之前选中的用户（如果在新的用户列表中存在）
                            var validSelected = currentSelected.filter(user => users.includes(user));
                            userSelect.val(validSelected).trigger('change');
                            document.getElementById('filterForm').submit();
                        });
                    } else {
                        $(this).val('CRM').trigger('change');
                    }
                });

                // 监听用户选择变化
                $('.select2-multi').on('change', function() {
                    document.getElementById('filterForm').submit();
                });

                // 监听周选择变化
                $('select[name="selectedWeek"]').on('change', function() {
                    document.getElementById('filterForm').submit();
                });
            });

            // 修改清除选择函数
            function clearSelection() {
                $('.select2-single').val('CRM').trigger('change');
                $('.select2-multi').val(null).trigger('change');
                $('select[name="selectedWeek"]').val('').trigger('change');
                document.getElementById('filterForm').submit();
            }

            // 获取后端数据
            var commits = /*[[${commits}]]*/ [];
            var weeks = /*[[${weeks}]]*/ [];
            var selectedUsers = /*[[${selectedUsers}]]*/ [];
            var selectedWeek = /*[[${selectedWeek}]]*/ '';

            // 处理数据
            var usernames = [...new Set(commits.map(item => item.username))];
            if (selectedUsers && selectedUsers.length > 0) {
                usernames = selectedUsers;
            }

            // 获取最近4周并按照周从小到大排序
            var weekIndex = weeks.indexOf(selectedWeek);
            var recentWeeks = weeks.slice(
                Math.max(0, weekIndex),
                Math.min(weekIndex + 4, weeks.length)
            ).sort((a, b) => a.localeCompare(b));

            // 准备柱状图数据
            var series = recentWeeks.map(function(week) {
                return {
                    name: week,
                    type: 'bar',
                    data: usernames.map(function(username) {
                        var commit = commits.find(function(c) {
                            return c.username === username && c.weekly === week;
                        });
                        return commit ? commit.totalLines : 0;
                    }),
                    label: {
                        show: true,
                        position: 'top',
                        formatter: function(params) {
                            return params.value || 0;
                        }
                    }
                };
            });

            // 初始化图表
            var chartDom = document.getElementById('weeklyChart');
            var myChart = echarts.init(chartDom);

            var option = {
                title: {
                    text: '研发人员周代码提交统计',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: recentWeeks,
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: usernames,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '代码行数'
                },
                series: series
            };

            // 添加表格数据处理逻辑
            function calculateIncrements() {
                var tableBody = document.querySelector('.increment-table tbody');
                tableBody.innerHTML = '';
                
                usernames.forEach(function(username) {
                    var row = document.createElement('tr');
                    
                    // 添加用户名列
                    var nameCell = document.createElement('td');
                    nameCell.textContent = username;
                    row.appendChild(nameCell);
                    
                    // 计算每周的增量
                    recentWeeks.forEach(function(week, index) {
                        var cell = document.createElement('td');
                        var currentValue = commits.find(c => c.username === username && c.weekly === week)?.totalLines || 0;
                        
                        if (index > 0) {
                            var prevWeek = recentWeeks[index - 1];
                            var prevValue = commits.find(c => c.username === username && c.weekly === prevWeek)?.totalLines || 0;
                            
                            if (prevValue > 0) {
                                var increment = ((currentValue - prevValue) / prevValue * 100).toFixed(1);
                                var span = document.createElement('span');
                                span.textContent = increment + '%';
                                
                                // 添加箭头
                                var arrow = document.createElement('span');
                                if (increment > 0) {
                                    arrow.textContent = '▲';
                                    arrow.className = 'arrow-up';
                                } else if (increment < 0) {
                                    arrow.textContent = '▼';
                                    arrow.className = 'arrow-down';
                                }
                                
                                cell.appendChild(span);
                                if (increment != 0) {
                                    cell.appendChild(arrow);
                                }
                            } else {
                                cell.textContent = '-';
                            }
                        } else {
                            cell.textContent = '-'; // 第一周没有增量
                        }
                        
                        row.appendChild(cell);
                    });
                    
                    tableBody.appendChild(row);
                });
            }

            // 在图表初始化后调用表格初始化
            myChart.setOption(option);
            calculateIncrements();

            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        </script>
    </div>
</body>
</html> 