<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${projectName != null ? projectName + ' - 项目分析报告' : '项目分析报告'}">项目分析报告</title>

    <!-- 引入Font Awesome图标库 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- 引入Markdown渲染库 -->
    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f5f5f5;
        }

        .report-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
            min-height: 100vh;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .report-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
        }
        
        .report-header h1 {
            margin: 0;
            font-size: 2.2em;
            font-weight: 300;
        }
        
        .report-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .back-button {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 8px 16px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            transition: all 0.3s;
        }
        
        .back-button:hover {
            background: rgba(255, 255, 255, 0.3);
            color: white;
            text-decoration: none;
        }
        
        .report-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 40px;
            line-height: 1.8;
            color: #333;
        }
        
        .report-content h1,
        .report-content h2,
        .report-content h3,
        .report-content h4,
        .report-content h5,
        .report-content h6 {
            margin-top: 32px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
            color: #2c3e50;
        }
        
        .report-content h1:first-child,
        .report-content h2:first-child,
        .report-content h3:first-child {
            margin-top: 0;
        }
        
        .report-content h1 {
            font-size: 2.2em;
            border-bottom: 2px solid #eaecef;
            padding-bottom: 12px;
            color: #2c3e50;
        }
        
        .report-content h2 {
            font-size: 1.8em;
            border-bottom: 1px solid #eaecef;
            padding-bottom: 8px;
            color: #34495e;
        }
        
        .report-content h3 {
            font-size: 1.4em;
            color: #34495e;
        }
        
        .report-content h4 {
            font-size: 1.2em;
            color: #34495e;
        }
        
        .report-content p {
            margin-bottom: 16px;
            text-align: left;
        }
        
        .report-content ul,
        .report-content ol {
            margin-bottom: 16px;
            padding-left: 30px;
        }
        
        .report-content li {
            margin-bottom: 6px;
            text-align: left;
        }
        
        .report-content table {
            border-collapse: collapse;
            width: 100%;
            margin-bottom: 20px;
            border: 1px solid #dfe2e5;
        }
        
        .report-content table th,
        .report-content table td {
            border: 1px solid #dfe2e5;
            padding: 12px 16px;
            text-align: left;
        }
        
        .report-content table th {
            background-color: #f6f8fa;
            font-weight: 600;
            color: #24292e;
        }
        
        .report-content table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .report-content blockquote {
            border-left: 4px solid #dfe2e5;
            padding: 0 16px;
            color: #6a737d;
            margin: 16px 0;
            background-color: #f6f8fa;
            border-radius: 0 6px 6px 0;
        }
        
        .report-content code {
            background-color: rgba(27,31,35,0.05);
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
        }
        
        .report-content pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            font-size: 85%;
            line-height: 1.45;
            overflow: auto;
            padding: 16px;
            margin-bottom: 16px;
            border: 1px solid #e1e4e8;
        }
        
        .report-content pre code {
            background-color: transparent;
            border: 0;
            display: inline;
            line-height: inherit;
            margin: 0;
            max-width: auto;
            overflow: visible;
            padding: 0;
            word-wrap: normal;
        }
        
        .error-state,
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }
        
        .error-state i,
        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }
        
        .error-state h3,
        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .error-state {
            color: #dc3545;
        }
        
        .error-state i {
            color: #dc3545;
        }
        

        
        @media print {
            .report-header,
            .back-button {
                display: none !important;
            }
            
            .report-container {
                max-width: none;
                margin: 0;
                padding: 0;
                box-shadow: none;
            }
            
            .report-content {
                box-shadow: none;
                padding: 20px;
            }
        }
        
        @media (max-width: 768px) {
            .report-container {
                padding: 10px;
            }
            
            .report-header {
                padding: 20px;
            }
            
            .report-header h1 {
                font-size: 1.8em;
            }
            
            .back-button {
                position: static;
                transform: none;
                margin-bottom: 15px;
                display: inline-block;
            }
            
            .report-content {
                padding: 20px;
            }
            

        }
    </style>
</head>

<body>
    <div class="report-container">
        <!-- 报告标题 -->
        <div class="report-header">
            <a href="javascript:window.close()" class="back-button">
                <i class="fas fa-times"></i> 关闭
            </a>
            <h1 th:text="${projectName != null ? projectName + ' - 项目分析报告' : '项目分析报告'}">项目分析报告</h1>
            <p>详细的项目质量分析和问题统计报告</p>
        </div>

        <!-- 报告内容 -->
        <div th:if="${hasContent}" class="report-content" id="reportContent">
            <!-- Markdown内容将通过JavaScript渲染到这里 -->
        </div>

        <!-- 无内容状态 -->
        <div th:if="${!hasContent and !error}" class="report-content">
            <div class="empty-state">
                <i class="fas fa-file-alt"></i>
                <h3>暂无报告内容</h3>
                <p th:text="${message}">该项目暂无分析报告内容</p>
            </div>
        </div>

        <!-- 错误状态 -->
        <div th:if="${error}" class="report-content">
            <div class="error-state">
                <i class="fas fa-exclamation-triangle"></i>
                <h3>加载失败</h3>
                <p th:text="${error}">报告加载失败</p>
            </div>
        </div>


    </div>

    <!-- 将分析内容放在隐藏的div中，避免JavaScript字符串转义问题 -->
    <div th:if="${hasContent}" id="analysisContentData" style="display: none;" th:text="${analysisContent}"></div>

    <script th:if="${hasContent}">
        // 确保marked库加载完成后再渲染内容
        function loadMarkedAndRender() {
            const analysisContentElement = document.getElementById('analysisContentData');
            const analysisContent = analysisContentElement ? analysisContentElement.textContent : '';
            const reportContentElement = document.getElementById('reportContent');

            console.log('开始渲染报告内容，内容长度：', analysisContent ? analysisContent.length : 0);
            console.log('marked库状态：', typeof marked !== 'undefined' ? '已加载' : '未加载');

            if (!analysisContent) {
                console.error('分析内容为空');
                reportContentElement.innerHTML = '<div class="error-state"><i class="fas fa-exclamation-triangle"></i><h3>内容为空</h3><p>该项目暂无分析报告内容</p></div>';
                return;
            }

            if (typeof marked !== 'undefined') {
                try {
                    // 配置marked选项
                    marked.setOptions({
                        breaks: true,
                        gfm: true
                    });

                    // 渲染Markdown内容
                    reportContentElement.innerHTML = marked.parse(analysisContent);

                    console.log('Markdown内容渲染完成');
                } catch (error) {
                    console.error('Markdown渲染失败：', error);
                    reportContentElement.innerHTML = '<div class="error-state"><i class="fas fa-exclamation-triangle"></i><h3>内容渲染失败</h3><p>Markdown内容解析出错：' + error.message + '</p></div>';
                }
            } else {
                // 如果marked库未加载，显示原始内容
                console.warn('marked库未加载，显示原始内容');
                reportContentElement.innerHTML = '<pre style="white-space: pre-wrap; font-family: inherit; line-height: 1.6;">' +
                    analysisContent.replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</pre>';
            }
        }

        // 页面加载完成后检查并渲染内容
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                // 给marked库一些时间加载
                setTimeout(loadMarkedAndRender, 100);
            });
        } else {
            // 如果页面已经加载完成
            setTimeout(loadMarkedAndRender, 100);
        }
    </script>
    </div>
</body>
</html>
