<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(~{::title}, ~{::head}, ~{::content})}">
<head>
    <title>项目统计分析</title>
    
    <style>
        /* 页面整体样式 */
        .stats-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            min-height: 100vh;
        }

        /* 页面标题样式 */
        .page-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .page-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .page-header h1 i {
            margin-right: 15px;
            font-size: 32px;
        }

        .page-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        /* 筛选条件样式 */
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .filter-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .filter-title i {
            font-size: 20px;
            color: #28a745;
            margin-right: 10px;
        }

        .filter-title h4 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }

        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .filter-group.wide {
            min-width: 300px;
        }

        .filter-group label {
            font-weight: 500;
            color: #555;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40,167,69,0.1);
        }

        .filter-actions {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn-search {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40,167,69,0.3);
        }

        .btn-reset {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            background: #5a6268;
        }

        .btn-export {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-export:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        /* 图表容器样式 */
        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .chart-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .chart-header i {
            font-size: 20px;
            margin-right: 10px;
        }

        .chart-header h5 {
            margin: 0;
            color: #333;
            font-weight: 600;
            font-size: 18px;
        }

        .chart-content {
            height: 400px;
            position: relative;
        }

        /* 大图表样式 */
        .chart-card.large {
            grid-column: 1 / -1;
        }

        .chart-card.large .chart-content {
            height: 500px;
        }

        /* 加载状态样式 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #6c757d;
        }

        .loading i {
            font-size: 24px;
            margin-right: 10px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 空数据状态样式 */
        .empty-state {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-state h5 {
            margin: 0 0 10px 0;
            font-weight: 500;
        }

        .empty-state p {
            margin: 0;
            font-size: 14px;
            opacity: 0.8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .stats-container {
                padding: 10px;
            }
            
            .charts-container {
                grid-template-columns: 1fr;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                min-width: auto;
            }
        }

        /* Select2 多选样式优化 */
        .select2-container--default .select2-selection--multiple {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            min-height: 38px;
        }

        .select2-container--default.select2-container--focus .select2-selection--multiple {
            border-color: #28a745;
            box-shadow: 0 0 0 3px rgba(40,167,69,0.1);
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
            border-radius: 4px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: white;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #dc3545;
        }
    </style>
</head>

<body>
<div th:fragment="content">
    <div class="stats-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>
                <i class="fas fa-chart-bar"></i>
                项目统计分析
            </h1>
            <p>以项目名称为维度进行横向对比分析，展示各个项目的开发问题、测试问题、灰度验收问题以及整体质量指标</p>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                <h4>筛选条件</h4>
            </div>
            <form id="filterForm">
                <div class="filter-row">
                    <div class="filter-group wide">
                        <label for="projectNames">项目名称（多选）</label>
                        <select id="projectNames" name="projectNames" multiple="multiple">
                            <option th:each="project : ${allProjectNames}" 
                                    th:value="${project}" 
                                    th:text="${project}"
                                    th:selected="${selectedProjectNames != null and selectedProjectNames.contains(project)}"></option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="grayDateStart">灰度日期开始</label>
                        <input type="date" id="grayDateStart" name="grayDateStart" th:value="${selectedGrayDateStart}">
                    </div>
                    <div class="filter-group">
                        <label for="grayDateEnd">灰度日期结束</label>
                        <input type="date" id="grayDateEnd" name="grayDateEnd" th:value="${selectedGrayDateEnd}">
                    </div>
                    <div class="filter-group">
                        <label for="onlineDateStart">上线日期开始</label>
                        <input type="date" id="onlineDateStart" name="onlineDateStart" th:value="${selectedOnlineDateStart}">
                    </div>
                    <div class="filter-group">
                        <label for="onlineDateEnd">上线日期结束</label>
                        <input type="date" id="onlineDateEnd" name="onlineDateEnd" th:value="${selectedOnlineDateEnd}">
                    </div>
                    <div class="filter-actions">
                        <button type="button" class="btn-search" onclick="loadAllCharts()">
                            <i class="fas fa-search"></i>
                            查询
                        </button>
                        <button type="button" class="btn-reset" onclick="resetFilters()">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                        <button type="button" class="btn-export" onclick="exportStats()">
                            <i class="fas fa-download"></i>
                            导出Excel
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 统计图表 -->
        <div class="charts-container">
            <!-- 项目对比统计图 -->
            <div class="chart-card large">
                <div class="chart-header">
                    <i class="fas fa-chart-column" style="color: #28a745;"></i>
                    <h5>项目对比统计</h5>
                </div>
                <div class="chart-content" id="projectComparisonChart"></div>
            </div>

            <!-- 项目质量指标对比图 -->
            <div class="chart-card large">
                <div class="chart-header">
                    <i class="fas fa-chart-line" style="color: #007bff;"></i>
                    <h5>项目质量指标对比</h5>
                </div>
                <div class="chart-content" id="projectQualityChart"></div>
            </div>

            <!-- 项目开发问题分类对比图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <i class="fas fa-bug" style="color: #dc3545;"></i>
                    <h5>开发问题分类对比</h5>
                </div>
                <div class="chart-content" id="devIssueComparisonChart"></div>
            </div>

            <!-- 项目测试问题分类对比图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <i class="fas fa-vial" style="color: #fd7e14;"></i>
                    <h5>测试问题分类对比</h5>
                </div>
                <div class="chart-content" id="testIssueComparisonChart"></div>
            </div>
        </div>
    </div>

    <script>
        /**
         * 项目统计分析页面JavaScript
         * <AUTHOR>
         * @date 2025-07-10 19:30:35
         */

        // 图表实例
        let projectComparisonChart = null;
        let projectQualityChart = null;
        let devIssueComparisonChart = null;
        let testIssueComparisonChart = null;

        // 页面加载完成后初始化
        $(document).ready(function() {
            console.log('=== 项目统计分析页面加载完成 ===');
            console.log('jQuery版本:', $.fn.jquery);
            console.log('ECharts是否加载:', typeof echarts !== 'undefined');
            console.log('SweetAlert2是否加载:', typeof Swal !== 'undefined');
            console.log('Select2是否加载:', typeof $.fn.select2 !== 'undefined');

            // 设置默认日期
            setDefaultDates();

            // 初始化Select2多选组件
            initializeSelect2();

            // 初始化图表
            initializeCharts();

            // 延迟加载图表数据，确保初始化完成
            setTimeout(() => {
                console.log('开始加载图表数据...');
                loadAllCharts();
            }, 1000);
        });

        /**
         * 设置默认日期
         */
        function setDefaultDates() {
            console.log('设置默认日期...');

            // 获取当前日期
            const today = new Date();
            const currentYear = today.getFullYear();

            // 格式化日期为 YYYY-MM-DD 格式
            const formatDate = (date) => {
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                return `${year}-${month}-${day}`;
            };

            // 设置上线开始日期为当年第一天（如果当前没有值）
            const onlineDateStartInput = $('#onlineDateStart');
            if (!onlineDateStartInput.val()) {
                const yearStart = new Date(currentYear, 0, 1); // 当年1月1日
                const yearStartStr = formatDate(yearStart);
                onlineDateStartInput.val(yearStartStr);
                console.log('设置上线开始日期为当年第一天：', yearStartStr);
            }

            // 设置上线结束日期为当前日期（如果当前没有值）
            const onlineDateEndInput = $('#onlineDateEnd');
            if (!onlineDateEndInput.val()) {
                const todayStr = formatDate(today);
                onlineDateEndInput.val(todayStr);
                console.log('设置上线结束日期为当前日期：', todayStr);
            }
        }

        /**
         * 初始化Select2多选组件
         */
        function initializeSelect2() {
            console.log('初始化Select2多选组件...');

            if (typeof $.fn.select2 !== 'undefined') {
                $('#projectNames').select2({
                    placeholder: '请选择项目（可多选）',
                    allowClear: true,
                    width: '100%',
                    language: {
                        noResults: function() {
                            return "未找到匹配项";
                        },
                        searching: function() {
                            return "搜索中...";
                        }
                    }
                });
                console.log('Select2初始化成功');
            } else {
                console.error('Select2未加载！');
            }
        }

        /**
         * 初始化所有图表
         */
        function initializeCharts() {
            console.log('开始初始化图表...');

            try {
                // 检查ECharts是否加载
                if (typeof echarts === 'undefined') {
                    console.error('ECharts未加载！');
                    return;
                }

                // 初始化项目对比统计图
                const comparisonElement = document.getElementById('projectComparisonChart');
                if (comparisonElement) {
                    projectComparisonChart = echarts.init(comparisonElement);
                    console.log('项目对比统计图初始化成功');
                } else {
                    console.error('找不到projectComparisonChart元素');
                }

                // 初始化项目质量指标对比图
                const qualityElement = document.getElementById('projectQualityChart');
                if (qualityElement) {
                    projectQualityChart = echarts.init(qualityElement);
                    console.log('项目质量指标对比图初始化成功');
                } else {
                    console.error('找不到projectQualityChart元素');
                }

                // 初始化开发问题分类对比图
                const devIssueElement = document.getElementById('devIssueComparisonChart');
                if (devIssueElement) {
                    devIssueComparisonChart = echarts.init(devIssueElement);
                    console.log('开发问题分类对比图初始化成功');
                } else {
                    console.error('找不到devIssueComparisonChart元素');
                }

                // 初始化测试问题分类对比图
                const testIssueElement = document.getElementById('testIssueComparisonChart');
                if (testIssueElement) {
                    testIssueComparisonChart = echarts.init(testIssueElement);
                    console.log('测试问题分类对比图初始化成功');
                } else {
                    console.error('找不到testIssueComparisonChart元素');
                }

                // 窗口大小改变时重新调整图表大小
                window.addEventListener('resize', function() {
                    projectComparisonChart && projectComparisonChart.resize();
                    projectQualityChart && projectQualityChart.resize();
                    devIssueComparisonChart && devIssueComparisonChart.resize();
                    testIssueComparisonChart && testIssueComparisonChart.resize();
                });

                console.log('所有图表初始化完成');
            } catch (error) {
                console.error('图表初始化失败：', error);
            }
        }

        /**
         * 加载所有图表数据
         */
        function loadAllCharts() {
            console.log('=== 开始加载图表数据 ===');

            // 检查图表是否已初始化
            if (!projectComparisonChart || !projectQualityChart ||
                !devIssueComparisonChart || !testIssueComparisonChart) {
                console.error('图表未正确初始化，重新初始化...');
                initializeCharts();

                // 延迟执行，确保图表初始化完成
                setTimeout(() => {
                    loadAllCharts();
                }, 500);
                return;
            }

            const params = getFilterParams();
            console.log('筛选参数：', params);

            // 显示加载状态
            showLoading();

            // 并行加载所有图表数据
            console.log('开始并行加载4个图表的数据...');
            Promise.all([
                loadProjectComparisonChart(params),
                loadProjectQualityChart(params),
                loadDevIssueComparisonChart(params),
                loadTestIssueComparisonChart(params)
            ]).then(() => {
                console.log('=== 所有图表数据加载完成 ===');
            }).catch(error => {
                console.error('=== 加载图表数据失败 ===', error);
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: '加载失败',
                        text: '加载图表数据失败，请稍后重试'
                    });
                } else {
                    alert('加载图表数据失败，请稍后重试');
                }
            });
        }

        /**
         * 获取筛选参数
         */
        function getFilterParams() {
            const projectNames = $('#projectNames').val();
            return {
                projectNames: projectNames && projectNames.length > 0 ? projectNames : null,
                grayDateStart: $('#grayDateStart').val(),
                grayDateEnd: $('#grayDateEnd').val(),
                onlineDateStart: $('#onlineDateStart').val(),
                onlineDateEnd: $('#onlineDateEnd').val()
            };
        }

        /**
         * 显示加载状态
         */
        function showLoading() {
            console.log('显示加载状态...');

            // 使用ECharts的showLoading方法
            if (projectComparisonChart) {
                projectComparisonChart.showLoading();
            }
            if (projectQualityChart) {
                projectQualityChart.showLoading();
            }
            if (devIssueComparisonChart) {
                devIssueComparisonChart.showLoading();
            }
            if (testIssueComparisonChart) {
                testIssueComparisonChart.showLoading();
            }
        }

        /**
         * 加载项目对比统计图
         */
        function loadProjectComparisonChart(params) {
            console.log('正在加载项目对比统计数据...');
            return $.get('/project-stats/api/project-comparison-stats', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const projects = Object.keys(data);
                        const categories = ['总问题数', '开发人员数', '测试人员数', '验收人员数', '平台数', '模块数'];

                        const seriesData = categories.map(category => ({
                            name: category,
                            type: 'bar',
                            data: projects.map(project => data[project][category] || 0),
                            emphasis: {
                                focus: 'series'
                            }
                        }));

                        const option = {
                            title: {
                                text: '项目基础数据对比',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            legend: {
                                data: categories,
                                top: 30,
                                type: 'scroll'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                top: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: projects,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: seriesData
                        };

                        projectComparisonChart.hideLoading();
                        projectComparisonChart.setOption(option);
                        console.log('项目对比统计图数据加载成功');
                    } else {
                        projectComparisonChart.hideLoading();
                        showEmptyState('projectComparisonChart', '暂无项目对比统计数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('项目对比统计数据加载失败：', error);
                    projectComparisonChart.hideLoading();
                    showEmptyState('projectComparisonChart', '加载项目对比统计数据失败');
                });
        }

        /**
         * 加载项目质量指标对比图
         */
        function loadProjectQualityChart(params) {
            console.log('正在加载项目质量指标数据...');
            return $.get('/project-stats/api/project-quality-stats', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const projects = Object.keys(data);

                        const seriesData = [
                            {
                                name: '测试用例覆盖率',
                                type: 'line',
                                yAxisIndex: 1,
                                data: projects.map(project => data[project]['测试用例覆盖率'] || 0),
                                itemStyle: { color: '#28a745' },
                                lineStyle: { width: 3 }
                            },
                            {
                                name: '可验收率',
                                type: 'line',
                                yAxisIndex: 1,
                                data: projects.map(project => data[project]['可验收率'] || 0),
                                itemStyle: { color: '#007bff' },
                                lineStyle: { width: 3 }
                            },
                            {
                                name: '完成率',
                                type: 'line',
                                yAxisIndex: 1,
                                data: projects.map(project => data[project]['完成率'] || 0),
                                itemStyle: { color: '#fd7e14' },
                                lineStyle: { width: 3 }
                            },
                            {
                                name: '开发问题数',
                                type: 'bar',
                                data: projects.map(project => data[project]['开发问题数'] || 0),
                                itemStyle: { color: '#dc3545' }
                            },
                            {
                                name: '测试问题数',
                                type: 'bar',
                                data: projects.map(project => data[project]['测试问题数'] || 0),
                                itemStyle: { color: '#6f42c1' }
                            }
                        ];

                        const option = {
                            title: {
                                text: '项目质量指标对比',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'cross'
                                }
                            },
                            legend: {
                                data: ['测试用例覆盖率', '可验收率', '完成率', '开发问题数', '测试问题数'],
                                top: 30
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                top: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: projects,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: [
                                {
                                    type: 'value',
                                    name: '问题数量',
                                    position: 'left'
                                },
                                {
                                    type: 'value',
                                    name: '百分比(%)',
                                    position: 'right',
                                    max: 100
                                }
                            ],
                            series: seriesData
                        };

                        projectQualityChart.hideLoading();
                        projectQualityChart.setOption(option);
                        console.log('项目质量指标对比图数据加载成功');
                    } else {
                        projectQualityChart.hideLoading();
                        showEmptyState('projectQualityChart', '暂无项目质量指标数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('项目质量指标数据加载失败：', error);
                    projectQualityChart.hideLoading();
                    showEmptyState('projectQualityChart', '加载项目质量指标数据失败');
                });
        }

        /**
         * 加载开发问题分类对比图
         */
        function loadDevIssueComparisonChart(params) {
            console.log('正在加载开发问题分类对比数据...');
            return $.get('/project-stats/api/project-dev-issue-comparison', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const projects = Object.keys(data);
                        const categories = [...new Set(Object.values(data).flatMap(project => Object.keys(project)))];

                        const seriesData = categories.map(category => ({
                            name: category,
                            type: 'bar',
                            stack: 'total',
                            data: projects.map(project => data[project][category] || 0)
                        }));

                        const option = {
                            title: {
                                text: '项目开发问题分类对比',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            legend: {
                                data: categories,
                                top: 30,
                                type: 'scroll'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                top: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: projects,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: seriesData
                        };

                        devIssueComparisonChart.hideLoading();
                        devIssueComparisonChart.setOption(option);
                        console.log('开发问题分类对比图数据加载成功');
                    } else {
                        devIssueComparisonChart.hideLoading();
                        showEmptyState('devIssueComparisonChart', '暂无开发问题分类对比数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('开发问题分类对比数据加载失败：', error);
                    devIssueComparisonChart.hideLoading();
                    showEmptyState('devIssueComparisonChart', '加载开发问题分类对比数据失败');
                });
        }

        /**
         * 加载测试问题分类对比图
         */
        function loadTestIssueComparisonChart(params) {
            console.log('正在加载测试问题分类对比数据...');
            return $.get('/project-stats/api/project-test-issue-comparison', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const projects = Object.keys(data);
                        const categories = [...new Set(Object.values(data).flatMap(project => Object.keys(project)))];

                        const seriesData = categories.map(category => ({
                            name: category,
                            type: 'bar',
                            stack: 'total',
                            data: projects.map(project => data[project][category] || 0)
                        }));

                        const option = {
                            title: {
                                text: '项目测试问题分类对比',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            legend: {
                                data: categories,
                                top: 30,
                                type: 'scroll'
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                top: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: projects,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: seriesData
                        };

                        testIssueComparisonChart.hideLoading();
                        testIssueComparisonChart.setOption(option);
                        console.log('测试问题分类对比图数据加载成功');
                    } else {
                        testIssueComparisonChart.hideLoading();
                        showEmptyState('testIssueComparisonChart', '暂无测试问题分类对比数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('测试问题分类对比数据加载失败：', error);
                    testIssueComparisonChart.hideLoading();
                    showEmptyState('testIssueComparisonChart', '加载测试问题分类对比数据失败');
                });
        }

        /**
         * 显示空数据状态
         */
        function showEmptyState(containerId, message) {
            console.log('显示空数据状态：', containerId, message);

            // 获取对应的图表实例
            let chart = null;
            switch(containerId) {
                case 'projectComparisonChart':
                    chart = projectComparisonChart;
                    break;
                case 'projectQualityChart':
                    chart = projectQualityChart;
                    break;
                case 'devIssueComparisonChart':
                    chart = devIssueComparisonChart;
                    break;
                case 'testIssueComparisonChart':
                    chart = testIssueComparisonChart;
                    break;
            }

            if (chart) {
                // 使用ECharts显示空数据
                const emptyOption = {
                    title: {
                        text: message,
                        left: 'center',
                        top: 'middle',
                        textStyle: {
                            color: '#999',
                            fontSize: 16
                        }
                    }
                };
                chart.setOption(emptyOption);
            }
        }

        /**
         * 重置筛选条件
         */
        function resetFilters() {
            $('#projectNames').val(null).trigger('change');
            $('#grayDateStart').val('');
            $('#grayDateEnd').val('');
            $('#onlineDateStart').val('');
            $('#onlineDateEnd').val('');

            // 重新设置默认日期
            setDefaultDates();

            loadAllCharts();
        }

        /**
         * 导出统计数据
         */
        function exportStats() {
            console.log('开始导出统计数据...');

            const params = getFilterParams();

            if (typeof Swal !== 'undefined') {
                Swal.fire({
                    title: '正在导出...',
                    text: '请稍候，正在生成Excel文件',
                    allowOutsideClick: false,
                    showConfirmButton: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });
            }

            $.get('/project-stats/api/export-stats', params)
                .done(function(response) {
                    if (response.success) {
                        if (typeof Swal !== 'undefined') {
                            Swal.fire({
                                icon: 'success',
                                title: '导出成功',
                                text: response.message,
                                confirmButtonText: '确定'
                            });
                        } else {
                            alert('导出成功：' + response.message);
                        }
                    } else {
                        if (typeof Swal !== 'undefined') {
                            Swal.fire({
                                icon: 'error',
                                title: '导出失败',
                                text: response.message
                            });
                        } else {
                            alert('导出失败：' + response.message);
                        }
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('导出失败：', error);
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            icon: 'error',
                            title: '导出失败',
                            text: '导出过程中发生错误，请稍后重试'
                        });
                    } else {
                        alert('导出失败，请稍后重试');
                    }
                });
        }

        // 键盘事件处理
        $(document).keydown(function(e) {
            // 按Enter键执行查询
            if (e.keyCode === 13) {
                loadAllCharts();
            }
        });
    </script>
</div>
</body>
</html>
