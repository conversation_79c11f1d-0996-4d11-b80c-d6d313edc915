<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::content}
      )}">
<head>
    <title>代码提交统计</title>
</head>
<body>
    <div th:fragment="content">
        <div class="filter-container">
            <form th:action="@{/commits/chart}" method="get" id="filterForm">
                <div class="filter-item">
                    <label class="filter-label">团队：</label>
                    <select name="selectedTeam" class="select2-single">
                        <option value="">全部团队</option>
                        <option th:each="team : ${teams}" 
                                th:value="${team}" 
                                th:text="${team}"
                                th:selected="${team == selectedTeam}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">用户名：</label>
                    <select name="selectedUsers" multiple class="select2-multi" data-placeholder="选择用户...">
                        <option th:each="username : ${usernames}" 
                                th:value="${username}" 
                                th:text="${username}"
                                th:selected="${selectedUsers != null && selectedUsers.contains(username)}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">起始月份：</label>
                    <input type="month" name="startMonth" th:value="${startMonth}">
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">结束月份：</label>
                    <input type="month" name="endMonth" th:value="${endMonth}">
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">&nbsp;</label>
                    <button type="submit">筛选</button>
                    <button type="button" onclick="clearSelection()">重置</button>
                </div>
            </form>
        </div>
        
        <div id="chart" style="width: 100%; height: 600px;"></div>
        
        <script th:inline="javascript">
            // 初始化Select2
            $(document).ready(function() {
                $('.select2-multi').select2({
                    allowClear: true,
                    closeOnSelect: false,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的用户";
                        }
                    }
                });
                
                $('.select2-single').select2({
                    allowClear: true,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的团队";
                        }
                    }
                });

                // 监听团队选择变化
                $('select[name="selectedTeam"]').on('change', function() {
                    var selectedTeam = $(this).val();
                    var userSelect = $('.select2-multi');
                    
                    if (selectedTeam) {
                        $.get('/commits/users-by-team', { team: selectedTeam }, function(users) {
                            var currentSelected = userSelect.val() || [];
                            userSelect.empty();
                            users.forEach(function(username) {
                                var newOption = new Option(username, username, false, false);
                                userSelect.append(newOption);
                            });
                            // 恢复之前选中的用户（如果在新的用户列表中存在）
                            var validSelected = currentSelected.filter(user => users.includes(user));
                            userSelect.val(validSelected).trigger('change');
                            // 自动提交表单
                            document.getElementById('filterForm').submit();
                        });
                    } else {
                        var allUsers = /*[[${usernames}]]*/ [];
                        var currentSelected = userSelect.val() || [];
                        userSelect.empty();
                        allUsers.forEach(function(username) {
                            var newOption = new Option(username, username, false, false);
                            userSelect.append(newOption);
                        });
                        // 恢复之前选中的用户
                        userSelect.val(currentSelected).trigger('change');
                        // 自动提交表单
                        document.getElementById('filterForm').submit();
                    }
                });

                // 监听用户选择变化
                $('.select2-multi').on('change', function() {
                    document.getElementById('filterForm').submit();
                });

                // 监听月份变化
                $('input[type="month"]').on('change', function() {
                    document.getElementById('filterForm').submit();
                });
            });

            // 修改清除选择函数
            function clearSelection() {
                $('.select2-multi').val(null).trigger('change');
                $('.select2-single').val(null).trigger('change');
                $('input[type="month"]').val('');
                document.getElementById('filterForm').submit();
            }

            // 获取后端传递的数据
            var commits = /*[[${commits}]]*/ [];
            var usernames = /*[[${usernames}]]*/ [];
            var months = /*[[${months}]]*/ [];
            var selectedUsers = /*[[${selectedUsers}]]*/ [];
            var startMonth = /*[[${startMonth}]]*/ '';
            var endMonth = /*[[${endMonth}]]*/ '';

            // 如果有选中的用户，只显示选中用户的数据
            if (selectedUsers && selectedUsers.length > 0) {
                usernames = selectedUsers;
            }

            // 过滤月份范围
            months = months.filter(function(month) {
                return (!startMonth || month >= startMonth) && (!endMonth || month <= endMonth);
            });

            // 处理数据
            var series = usernames.map(function(username) {
                var data = months.map(function(month) {
                    var commit = commits.find(function(c) {
                        return c.username === username && c.monthly === month;
                    });
                    return commit ? commit.totalLines : 0;
                });
                
                return {
                    name: username,
                    type: 'line',
                    data: data,
                    smooth: true
                };
            });

            // 初始化ECharts实例
            var chartDom = document.getElementById('chart');
            var myChart = echarts.init(chartDom);

            // 配置项
            var option = {
                title: {
                    text: '研发人员每月代码提交行数统计'
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data: usernames,
                    type: 'scroll',
                    orient: 'vertical',
                    right: 10,
                    top: 20,
                    bottom: 20
                },
                grid: {
                    left: '3%',
                    right: '15%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: months,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '代码行数'
                },
                series: series
            };

            // 使用配置项显示图表
            myChart.setOption(option);

            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        </script>
    </div>
</body>
</html> 