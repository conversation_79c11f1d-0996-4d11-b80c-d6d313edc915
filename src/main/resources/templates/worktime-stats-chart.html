<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::content}
      )}">
<head>
    <title>研发交付工时统计</title>
    <style>
        .chart-container {
            padding: 20px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px;
        }
        #workTimeChart {
            width: 100%;
            height: 600px;
        }
    </style>
</head>
<body>
    <div th:fragment="content">
        <div class="filter-container">
            <form th:action="@{/worktime-stats/chart}" method="get" id="filterForm">
                <div class="filter-item">
                    <label class="filter-label">团队：</label>
                    <select name="selectedTeam" class="select2-single">
                        <option value="">全部团队</option>
                        <option th:each="team : ${teams}" 
                                th:value="${team}" 
                                th:text="${team}"
                                th:selected="${team == selectedTeam || (selectedTeam == null && team == 'CRM')}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">&nbsp;</label>
                    <button type="button" onclick="clearSelection()">重置</button>
                </div>
            </form>
        </div>

        <div class="chart-container">
            <div id="workTimeChart"></div>
        </div>

        <script th:inline="javascript">
            // 初始化Select2
            $(document).ready(function() {
                $('.select2-single').select2({
                    allowClear: true,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的团队";
                        }
                    }
                });

                // 监听团队选择变化
                $('select[name="selectedTeam"]').on('change', function() {
                    document.getElementById('filterForm').submit();
                });
            });

            // 修改清除选择函数
            function clearSelection() {
                $('.select2-single').val('CRM').trigger('change');
            }

            // 获取后端数据
            var stats = /*[[${stats}]]*/ [];
            var selectedTeam = /*[[${selectedTeam}]]*/ '';

            // 过滤数据
            if (selectedTeam) {
                stats = stats.filter(function(item) {
                    return item.团队 === selectedTeam;
                });
            }

            // 准备图表数据
            var names = [...new Set(stats.map(item => item.名字))];
            var deliveryData = stats.map(item => item.交付时长 || 0);
            var totalData = stats.map(item => item.填写总时长 || 0);
            var theoreticalData = stats.map(item => item.理论时长 || 0);

            // 初始化图表
            var chartDom = document.getElementById('workTimeChart');
            var myChart = echarts.init(chartDom);

            var option = {
                title: {
                    text: '研发人员工时统计',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: ['交付时长', '填写总时长', '理论时长'],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: names,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '小时'
                },
                series: [
                    {
                        name: '交付时长',
                        type: 'bar',
                        data: deliveryData,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function(params) {
                                return params.value.toFixed(1);
                            }
                        }
                    },
                    {
                        name: '填写总时长',
                        type: 'bar',
                        data: totalData,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function(params) {
                                return params.value.toFixed(1);
                            }
                        }
                    },
                    {
                        name: '理论时长',
                        type: 'bar',
                        data: theoreticalData,
                        label: {
                            show: true,
                            position: 'top',
                            formatter: function(params) {
                                return params.value.toFixed(1);
                            }
                        }
                    }
                ]
            };

            myChart.setOption(option);

            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        </script>
    </div>
</body>
</html> 