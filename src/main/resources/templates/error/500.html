<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" th:replace="~{layout/base :: html(~{::title}, ~{::head}, ~{::content})}">
<head>
    <title>服务器错误 - 系统异常</title>
    <style>
        .error-container {
            max-width: 800px;
            margin: 50px auto;
            padding: 30px;
            text-align: center;
            font-family: Arial, sans-serif;
        }
        .error-code {
            font-size: 72px;
            color: #dc3545;
            font-weight: bold;
            margin-bottom: 20px;
        }
        .error-title {
            font-size: 32px;
            color: #343a40;
            margin-bottom: 20px;
        }
        .error-message {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 30px;
            line-height: 1.5;
        }
        .error-details {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin-bottom: 30px;
            text-align: left;
        }
        .btn-group {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        .btn-primary:hover {
            background-color: #0056b3;
        }
        .btn-secondary {
            background-color: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background-color: #545b62;
        }
    </style>
</head>
<body th:fragment="content">
    <div class="error-container">
        <div class="error-code">500</div>
        <div class="error-title">服务器内部错误</div>
        <div class="error-message">
            抱歉，服务器在处理您的请求时发生了错误。<br>
            我们的技术团队已收到通知，正在努力解决这个问题。
        </div>
        
        <div class="error-details" th:if="${error}">
            <h4>错误详情：</h4>
            <p th:text="${error}">错误信息</p>
        </div>
        
        <div class="btn-group">
            <a href="/" class="btn btn-primary">
                <i class="fas fa-home"></i> 返回首页
            </a>
            <a href="javascript:history.back()" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回上页
            </a>
        </div>
    </div>
    
    <script>
        // 自动刷新页面（可选）
        setTimeout(function() {
            console.log('页面已加载完成');
        }, 1000);
    </script>
</body>
</html>