<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::content}
      )}">
<head>
    <title>产线Bug统计</title>
    <style>
        .chart-container {
            padding: 20px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 20px;
        }
        .chart {
            width: 100%;
            height: 400px;
        }
    </style>
</head>
<body>
    <div th:fragment="content">
        <div class="filter-container">
            <form th:action="@{/product-line/bugs}" method="get" id="filterForm">
                <div class="filter-item">
                    <label class="filter-label">一级分类：</label>
                    <select name="selectedCategory" class="select2-single">
                        <option value="">全部分类</option>
                        <option th:each="category : ${categories}" 
                                th:value="${category}" 
                                th:text="${category}"
                                th:selected="${category == selectedCategory}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">起始月份：</label>
                    <input type="month" name="startMonth" th:value="${startMonth}">
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">结束月份：</label>
                    <input type="month" name="endMonth" th:value="${endMonth}">
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">&nbsp;</label>
                    <button type="submit">筛选</button>
                    <button type="button" onclick="clearSelection()">重置</button>
                </div>
            </form>
        </div>

        <div class="chart-container">
            <div id="trendChart" class="chart"></div>
        </div>

        <div class="chart-container">
            <div id="bugChart" class="chart"></div>
        </div>

        <script th:inline="javascript">
            // 获取后端数据
            var statistics = /*[[${statistics}]]*/ {};
            var categories = /*[[${categories}]]*/ [];
            
            // 准备柱状图数据
            var months = Object.keys(statistics);
            var selectedCategory = /*[[${selectedCategory}]]*/ '';
            var displayCategories = selectedCategory ? [selectedCategory] : categories;

            // 准备柱状图数据
            var series = displayCategories.map(function(category) {
                return {
                    name: category,
                    type: 'bar',
                    data: months.map(function(month) {
                        return statistics[month][category] || 0;
                    })
                };
            });

            // 准备趋势图数据
            var trendSeries = displayCategories.map(function(category) {
                return {
                    name: category,
                    type: 'line',
                    data: months.map(function(month) {
                        return statistics[month][category] || 0;
                    }),
                    smooth: true,
                    symbol: 'circle',
                    symbolSize: 6,
                    lineStyle: {
                        width: 2
                    }
                };
            });

            // 初始化柱状图
            var chartDom = document.getElementById('bugChart');
            var myChart = echarts.init(chartDom);

            var option = {
                title: {
                    text: '产线Bug分类统计',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                legend: {
                    data: displayCategories,
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: months,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    name: 'Bug数量'
                },
                series: series
            };

            myChart.setOption(option);

            // 初始化趋势图
            var trendDom = document.getElementById('trendChart');
            var trendChart = echarts.init(trendDom);

            var trendOption = {
                title: {
                    text: '产线Bug趋势分析',
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross'
                    }
                },
                legend: {
                    data: displayCategories,
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: months,
                    axisLabel: {
                        rotate: 45
                    },
                    boundaryGap: false
                },
                yAxis: {
                    type: 'value',
                    name: 'Bug数量'
                },
                series: trendSeries
            };

            trendChart.setOption(trendOption);

            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
                trendChart.resize();
            });

            // 初始化Select2
            $(document).ready(function() {
                $('.select2-single').select2({
                    allowClear: true,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的分类";
                        }
                    }
                });

                // 分类选择变化监听
                $('select[name="selectedCategory"]').on('change', function() {
                    document.getElementById('filterForm').submit();
                });

                // 月份变化监听
                $('input[type="month"]').on('change', function() {
                    document.getElementById('filterForm').submit();
                });
            });

            // 修改清除选择函数
            function clearSelection() {
                $('.select2-single').val('').trigger('change');
                $('input[type="month"]').val('');
                document.getElementById('filterForm').submit();
            }
        </script>
    </div>
</body>
</html> 