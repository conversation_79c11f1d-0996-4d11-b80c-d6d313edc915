<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::content}
      )}">
<head>
    <title>历年代码提交量统计报表</title>
</head>
<body>
    <div th:fragment="content">
        <h2>历年代码提交量统计报表</h2>

        <!-- 筛选条件区域 -->
        <div style="margin-bottom: 20px; background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <div style="display: flex; gap: 15px; align-items: center; flex-wrap: wrap;">
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label style="font-size: 14px; color: #333; white-space: nowrap;">统计维度：</label>
                    <select id="dimensionType" style="padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;">
                        <option value="YEAR">按年度统计</option>
                        <option value="HALF_YEAR" selected>按半年度统计</option>
                        <option value="QUARTER">按季度统计</option>
                        <option value="TEAM_YEAR">团队按年度</option>
                        <option value="TEAM_HALF_YEAR">团队按半年度</option>
                        <option value="TEAM_QUARTER">团队按季度</option>
                    </select>
                </div>
                <div style="display: flex; align-items: center; gap: 10px;">
                    <label style="font-size: 14px; color: #333; white-space: nowrap;">团队选择：</label>
                    <select id="teamSelect" multiple style="min-width: 200px; padding: 6px 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;" data-placeholder="选择团队（可多选）">
                        <option th:each="team : ${allTeams}" 
                                th:value="${team}" 
                                th:text="${team}"></option>
                    </select>
                </div>
                <div style="display: flex; gap: 10px; margin-left: auto;">
                    <button type="button" onclick="loadStatsData()" style="padding: 6px 20px; background: #0d6efd; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                        查询
                    </button>
                    <button type="button" onclick="exportData()" style="padding: 6px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 14px;">
                        导出
                    </button>
                </div>
            </div>
        </div>
        
        
        <!-- 数据表格区域 -->
        <div style="margin: 20px 0;">
            <h3>详细数据</h3>
            <div style="margin-top: 10px;">
                <table id="statsTable" style="width: 100%; border-collapse: collapse; border: 1px solid #ddd;">
                    <thead style="background-color: #343a40; color: white;">
                        <tr id="tableHeader">
                            <th style="padding: 10px; border: 1px solid #ddd;">时间周期</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">代码行数</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">开发人员数</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">人均行数</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">增长率</th>
                        </tr>
                    </thead>
                    <tbody id="tableBody">
                        <tr>
                            <td colspan="5" style="padding: 20px; text-align: center; color: #666; border: 1px solid #ddd;">请选择条件后点击查询按钮</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 图表展示区域 -->
        <div style="margin: 20px 0;">
            <h3>趋势图表</h3>
            <div id="chartContainer" style="height: 400px; width: 100%; border: 1px solid #ddd; border-radius: 5px; margin-top: 10px;">
                <div style="text-align: center; padding: 50px; color: #666;">
                    图表加载中...
                </div>
            </div>
        </div>

    <script th:inline="javascript">
    /*<![CDATA[*/
        // 页面初始化
        $(document).ready(function() {
            // 初始化Select2组件
            $('#teamSelect').select2({
                placeholder: '选择团队（可多选）',
                allowClear: true,
                width: 'resolve'
            });
            
            // 初始化图表
            initChart();
            
            // 加载默认数据
            loadStatsData();
        });

        let statsChart;

        // 初始化ECharts图表
        function initChart() {
            statsChart = echarts.init(document.getElementById('chartContainer'));
            
            // 设置初始加载状态
            statsChart.showLoading({
                text: '数据加载中...',
                color: '#0d6efd',
                maskColor: 'rgba(255, 255, 255, 0.8)'
            });
        }

        // 加载统计数据
        function loadStatsData() {
            const teams = $('#teamSelect').val();
            const dimensionType = $('#dimensionType').val();
            
            const request = {
                years: null, // 查询全部年份
                teams: teams && teams.length > 0 ? teams : null,
                dimensionType: dimensionType
            };
            
            // 显示图表加载状态
            if (statsChart) {
                statsChart.showLoading({
                    text: '数据加载中...',
                    color: '#0d6efd',
                    maskColor: 'rgba(255, 255, 255, 0.8)'
                });
            }
            
            // 发送AJAX请求
            $.ajax({
                url: '/commit-stats/data',
                method: 'POST',
                contentType: 'application/json',
                data: JSON.stringify(request),
                success: function(data) {
                    updateChart(data);
                    updateTable(data);
                },
                error: function(xhr, status, error) {
                    if (statsChart) {
                        statsChart.hideLoading();
                    }
                    showErrorMessage('数据加载失败: ' + error);
                    console.error('请求失败:', error);
                }
            });
        }


        // 更新图表
        function updateChart(data) {
            if (!statsChart) return;
            
            const dimensionType = $('#dimensionType').val();
            
            let option;
            if (dimensionType.includes('TEAM')) {
                option = getTeamComparisonChartOption(data);
            } else {
                option = getTimeTrendChartOption(data);
            }
            
            statsChart.hideLoading();
            statsChart.setOption(option, true);
        }

        // 获取时间趋势图表配置
        function getTimeTrendChartOption(data) {
            const xData = [...new Set(data.map(item => item.period))].sort();
            const yData = xData.map(period => {
                const item = data.find(d => d.period === period);
                return item ? item.totalLines : 0;
            });
            
            return {
                title: {
                    text: '代码提交量趋势图',
                    left: 'center',
                    textStyle: {
                        fontSize: 16
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        return params[0].name + '<br/>' + 
                               params[0].marker + '代码行数: ' + formatNumber(params[0].value);
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: xData,
                    axisTick: {
                        alignWithLabel: true
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: function(value) {
                            return formatNumber(value);
                        }
                    }
                },
                series: [{
                    name: '代码行数',
                    type: 'line',
                    data: yData,
                    smooth: true,
                    itemStyle: {
                        color: '#0d6efd'
                    },
                    areaStyle: {
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                {offset: 0, color: 'rgba(13, 110, 253, 0.3)'},
                                {offset: 1, color: 'rgba(13, 110, 253, 0.1)'}
                            ]
                        }
                    }
                }]
            };
        }

        // 获取团队对比图表配置
        function getTeamComparisonChartOption(data) {
            const teams = [...new Set(data.map(item => item.team))];
            const periods = [...new Set(data.map(item => item.period))].sort();
            
            const colors = ['#0d6efd', '#198754', '#ffc107', '#dc3545', '#6f42c1', '#fd7e14', '#20c997', '#6c757d'];
            
            const series = teams.map((team, index) => ({
                name: team,
                type: 'bar',
                data: periods.map(period => {
                    const item = data.find(d => d.team === team && d.period === period);
                    return item ? item.totalLines : 0;
                }),
                itemStyle: {
                    color: colors[index % colors.length]
                }
            }));
            
            return {
                title: {
                    text: '各团队代码提交量对比',
                    left: 'center',
                    textStyle: {
                        fontSize: 16
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            result += param.marker + param.seriesName + ': ' + formatNumber(param.value) + '<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: teams,
                    bottom: 0,
                    type: 'scroll'
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '15%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: periods
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: function(value) {
                            return formatNumber(value);
                        }
                    }
                },
                series: series
            };
        }

        // 更新表格
        function updateTable(data) {
            const dimensionType = $('#dimensionType').val();
            
            // 生成表头
            let headerHtml = '';
            if (dimensionType.includes('TEAM')) {
                headerHtml += '<th style="padding: 10px; border: 1px solid #ddd;">时间周期</th><th style="padding: 10px; border: 1px solid #ddd;">团队</th><th style="padding: 10px; border: 1px solid #ddd;">代码行数</th><th style="padding: 10px; border: 1px solid #ddd;">开发人员数</th><th style="padding: 10px; border: 1px solid #ddd;">占比</th><th style="padding: 10px; border: 1px solid #ddd;">增长率</th>';
            } else {
                headerHtml += '<th style="padding: 10px; border: 1px solid #ddd;">时间周期</th><th style="padding: 10px; border: 1px solid #ddd;">代码行数</th><th style="padding: 10px; border: 1px solid #ddd;">开发人员数</th><th style="padding: 10px; border: 1px solid #ddd;">人均行数</th><th style="padding: 10px; border: 1px solid #ddd;">增长率</th>';
                // 如果是季度或半年度统计，添加同比增长率列
                if (dimensionType === 'QUARTER' || dimensionType === 'HALF_YEAR') {
                    headerHtml += '<th style="padding: 10px; border: 1px solid #ddd;">同比增长率</th>';
                }
            }
            $('#tableHeader').html(headerHtml);
            
            // 生成数据行
            let bodyHtml = '';
            if (data.length === 0) {
                let colSpan = dimensionType.includes('TEAM') ? 6 : 5;
                // 如果是季度或半年度统计，增加同比增长率列
                if ((dimensionType === 'QUARTER' || dimensionType === 'HALF_YEAR') && !dimensionType.includes('TEAM')) {
                    colSpan = 6;
                }
                bodyHtml = `<tr><td colspan="${colSpan}" style="padding: 20px; text-align: center; color: #666; border: 1px solid #ddd;">暂无数据</td></tr>`;
            } else {
                data.forEach((item, index) => {
                    const rowStyle = index % 2 === 0 ? 'background-color: #f8f9fa;' : '';
                    bodyHtml += `<tr style="${rowStyle}">`;
                    bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd;">${item.period}</td>`;
                    if (dimensionType.includes('TEAM')) {
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd;"><span style="background: #17a2b8; color: white; padding: 2px 8px; border-radius: 3px;">${item.team || '未分配'}</span></td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatNumber(item.totalLines)}</td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${item.developerCount || 0}</td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${(item.percentage || 0).toFixed(2)}%</td>`;
                    } else {
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatNumber(item.totalLines)}</td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: center;">${item.developerCount || 0}</td>`;
                        bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; text-align: right;">${formatNumber(item.avgLinesPerDeveloper || 0)}</td>`;
                    }
                    const growthRate = item.growthRate || 0;
                    const growthColor = growthRate > 0 ? '#dc3545' : (growthRate < 0 ? '#28a745' : '#6c757d');
                    const growthIcon = growthRate > 0 ? '↗' : (growthRate < 0 ? '↘' : '-');
                    const estimatedLabel = item.isEstimated ? '<span style="color: #a306f6; font-size: 12px; margin-left: 4px;">(预估)</span>' : '';
                    bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; color: ${growthColor}; font-weight: bold;">${growthIcon} ${Math.abs(growthRate).toFixed(2)}%${estimatedLabel}</td>`;
                    
                    // 如果是季度或半年度统计且非团队维度，添加同比增长率列
                    if ((dimensionType === 'QUARTER' || dimensionType === 'HALF_YEAR') && !dimensionType.includes('TEAM')) {
                        const yearOverYearRate = item.yearOverYearGrowthRate;
                        if (yearOverYearRate !== null && yearOverYearRate !== undefined) {
                            const yoyColor = yearOverYearRate > 0 ? '#dc3545' : (yearOverYearRate < 0 ? '#28a745' : '#6c757d');
                            const yoyIcon = yearOverYearRate > 0 ? '↗' : (yearOverYearRate < 0 ? '↘' : '-');
                            const yoyEstimatedLabel = item.isEstimated ? '<span style="color: #a306f6; font-size: 12px; margin-left: 4px;">(预估)</span>' : '';
                            bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; color: ${yoyColor}; font-weight: bold;">${yoyIcon} ${Math.abs(yearOverYearRate).toFixed(2)}%${yoyEstimatedLabel}</td>`;
                        } else {
                            bodyHtml += `<td style="padding: 10px; border: 1px solid #ddd; color: #6c757d; text-align: center;">-</td>`;
                        }
                    }
                    
                    bodyHtml += '</tr>';
                });
            }
            $('#tableBody').html(bodyHtml);
        }

        // 导出数据
        function exportData() {
            const teams = $('#teamSelect').val();
            const dimensionType = $('#dimensionType').val();
            
            const request = {
                years: null, // 查询全部年份
                teams: teams && teams.length > 0 ? teams : null,
                dimensionType: dimensionType
            };
            
            // 创建临时表单提交
            const form = $('<form>', {
                'method': 'POST',
                'action': '/commit-stats/export'
            });
            
            form.append($('<input>', {
                'type': 'hidden',
                'name': 'requestData',
                'value': JSON.stringify(request)
            }));
            
            $('body').append(form);
            form.submit().remove();
        }

        // 工具函数
        function formatNumber(num) {
            if (!num) return '0';
            if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'K';
            }
            return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
        }

        function showErrorMessage(message) {
            // 使用Bootstrap的Toast或者简单的alert
            if (typeof bootstrap !== 'undefined' && bootstrap.Toast) {
                // TODO: 实现Bootstrap Toast提示
                console.error(message);
            } else {
                alert(message);
            }
        }
    /*]]>*/
    </script>
    </div>
</body>
</html>