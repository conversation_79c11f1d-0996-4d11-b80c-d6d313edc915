<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/public-base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::section}
      )}">
<head>
    <title>数据仓库表同步配置</title>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <!-- 添加Bootstrap Modal -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: flex-end;
            margin: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .filter-item {
            margin-bottom: 10px;
        }
        .filter-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }
        .filter-input {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .table-container {
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            margin: 20px;
            overflow-x: auto;
        }
        .sync-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        .sync-table th, .sync-table td {
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            color: #333;
        }
        .sync-table th {
            background-color: #f0f2f5;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            border-bottom: 2px solid #ddd;
        }
        .sync-table th:hover {
            background-color: #e5e8ed;
        }
        .sync-table tbody tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .sync-table tbody tr:hover {
            background-color: #f0f7ff;
            transition: background-color 0.2s ease;
        }
        .no-data {
            text-align: center;
            padding: 30px;
            color: #888;
            font-style: italic;
        }
        #searchBtn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        #searchBtn:hover {
            background-color: #45a049;
        }
        .select2-container--default .select2-selection--single {
            height: 38px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }
        .action-btn {
            margin-right: 5px;
            padding: 2px 8px;
            border-radius: 3px;
            cursor: pointer;
        }
        .edit-btn {
            background-color: #4a90e2;
            color: white;
            border: none;
        }
        .edit-btn:hover {
            background-color: #357abd;
        }
        .delete-btn {
            background-color: #dc3545;
            color: white;
            border: none;
        }
        .delete-btn:hover {
            background-color: #c82333;
        }
        .add-btn {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 5px 10px;
            margin-top: 10px;
            float: right;
        }
        .add-btn:hover {
            background-color: #45a049;
        }
        .table-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
    </style>
    <script th:inline="javascript">
        $(document).ready(function() {
            // 初始化Select2
            $('.select2').select2({
                width: '100%',
                placeholder: '请选择'
            });
            
            // 初始化DataTable
            $('#syncTable').DataTable({
                paging: true,
                searching: false,
                info: true,
                ordering: true,
                pageLength: 10,
                language: {
                    emptyTable: "暂无数据",
                    lengthMenu: "每页 _MENU_ 条记录",
                    info: "显示第 _START_ 至 _END_ 条记录，共 _TOTAL_ 条",
                    infoEmpty: "显示第 0 至 0 条记录，共 0 条",
                    infoFiltered: "(由 _MAX_ 条记录过滤)",
                    paginate: {
                        first: "首页",
                        previous: "上页",
                        next: "下页",
                        last: "末页"
                    }
                },
                columnDefs: [
                    { className: "dt-center", targets: "_all" }
                ]
            });
            
            // 查询按钮点击事件
            $('#searchBtn').click(function() {
                var sourceDb = $('#sourceDb').val();
                var sourceTable = $('#sourceTable').val();
                var targetTable = $('#targetTable').val();
                var syncType = $('#syncType').val();
                var isAdmin = /*[[${isAdmin}]]*/false;

                // 构建查询URL
                var url = '/table-sync/configs?';
                if (sourceDb) url += 'sourceDb=' + encodeURIComponent(sourceDb) + '&';
                if (sourceTable) url += 'sourceTable=' + encodeURIComponent(sourceTable) + '&';
                if (targetTable) url += 'targetTable=' + encodeURIComponent(targetTable) + '&';
                if (syncType) url += 'syncType=' + encodeURIComponent(syncType) + '&';
                if (isAdmin) url += 'ds-admin=true';
                
                // 跳转到查询URL
                window.location.href = url;
            });
            
            // 响应式调整
            $(window).resize(function() {
                $('#syncTable').DataTable().columns.adjust();
            });
            
            // 添加按钮点击事件
            $('#addBtn').click(function() {
                // 清空表单
                $('#configForm')[0].reset();
                $('#configId').val('');
                $('.select2').trigger('change');
                
                // 显示弹窗
                $('#configModal').modal('show');
            });
            
            // 编辑按钮点击事件
            $(document).on('click', '.edit-btn', function() {
                var id = $(this).data('id');
                
                // 获取配置详情
                $.ajax({
                    url: '/table-sync/config/' + id,
                    type: 'GET',
                    success: function(data) {
                        // 填充表单
                        $('#configId').val(data.id);
                        $('#modalSourceDb').val(data.sourceDb);
                        $('#modalSourceTable').val(data.sourceTable);
                        $('#modalTargetTable').val(data.targetTable);
                        $('#modalSyncType').val(data.syncType);
                        $('.select2').trigger('change');
                        
                        // 显示弹窗
                        $('#configModal').modal('show');
                    },
                    error: function(xhr) {
                        alert('获取配置失败：' + xhr.responseText);
                    }
                });
            });
            
            // 删除按钮点击事件
            $(document).on('click', '.delete-btn', function() {
                var id = $(this).data('id');
                var sourceDb = $(this).data('source-db');
                var sourceTable = $(this).data('source-table');
                
                if (confirm('确定要删除配置 "' + sourceDb + '.' + sourceTable + '" 吗？')) {
                    // 发送删除请求
                    $.ajax({
                        url: '/table-sync/config/' + id + '/delete',
                        type: 'POST',
                        success: function(response) {
                            if (response.success) {
                                alert(response.message);
                                // 刷新页面
                                location.reload();
                            } else {
                                alert('删除失败：' + response.message);
                            }
                        },
                        error: function(xhr) {
                            alert('删除失败：' + xhr.responseText);
                        }
                    });
                }
            });
            
            // 保存按钮点击事件
            $('#saveBtn').click(function() {
                var config = {
                    id: $('#configId').val() || null,
                    sourceDb: $('#modalSourceDb').val(),
                    sourceTable: $('#modalSourceTable').val(),
                    targetTable: $('#modalTargetTable').val(),
                    syncType: $('#modalSyncType').val()
                };
                
                // 验证表单
                if (!config.sourceDb) {
                    alert('请选择源库名称');
                    return;
                }
                if (!config.sourceTable) {
                    alert('请输入源表名称');
                    return;
                }
                if (!config.targetTable) {
                    alert('请输入目标表名称');
                    return;
                }
                if (!config.syncType) {
                    alert('请选择接入方式');
                    return;
                }
                
                // 保存配置
                $.ajax({
                    url: '/table-sync/config/save',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(config),
                    success: function(response) {
                        if (response.success) {
                            alert(response.message);
                            $('#configModal').modal('hide');
                            // 刷新页面
                            location.reload();
                        } else {
                            alert('操作失败：' + response.message);
                        }
                    },
                    error: function(xhr) {
                        alert('操作失败：' + xhr.responseText);
                    }
                });
            });
        });
    </script>
</head>
<body>
    <section>
        <h2>数据仓库表同步配置</h2>
        
        <!-- 查询条件区 -->
        <div class="filter-container">
            <div class="filter-item">
                <label class="filter-label">源库名称</label>
                <select id="sourceDb" class="select2">
                    <option value="">全部</option>
                    <option th:each="db : ${sourceDbs}"
                            th:value="${db}" 
                            th:text="${db}"
                            th:selected="${db == queryParam.sourceDb}"></option>
                </select>
            </div>
            
            <div class="filter-item">
                <label class="filter-label">源表名称</label>
                <input type="text" id="sourceTable" class="filter-input" 
                       th:value="${queryParam.sourceTable}" placeholder="请输入源表名称">
            </div>
            
            <div class="filter-item">
                <label class="filter-label">目标表名称</label>
                <input type="text" id="targetTable" class="filter-input" 
                       th:value="${queryParam.targetTable}" placeholder="请输入目标表名称">
            </div>
            
            <div class="filter-item">
                <label class="filter-label">接入方式</label>
                <select id="syncType" class="select2">
                    <option value="">全部</option>
                    <option th:each="type : ${syncTypes}"
                            th:value="${type}" 
                            th:text="${type}"
                            th:selected="${type == queryParam.syncType}"></option>
                </select>
            </div>
            
            <div class="filter-item">
                <button id="searchBtn">查询</button>
            </div>
        </div>
        
        <!-- 数据表格区 -->
        <div class="table-container">
            <!-- 表格标题和新增按钮 -->
            <div class="table-header" th:if="${isAdmin}">
                <h3>配置列表</h3>
                <button id="addBtn" class="add-btn">新增配置</button>
            </div>
            
            <table id="syncTable" class="sync-table">
                <thead>
                    <tr>
                        <th>源库名称</th>
                        <th>源表名称</th>
                        <th>目标表名称</th>
                        <th>接入方式</th>
                        <th th:if="${isAdmin}">操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:if="${configs.empty}">
                        <td colspan="${isAdmin ? 5 : 4}" class="no-data">暂无数据</td>
                    </tr>
                    <tr th:each="config : ${configs}">
                        <td th:text="${config.sourceDb}"></td>
                        <td th:text="${config.sourceTable}"></td>
                        <td th:text="${config.targetTable}"></td>
                        <td th:text="${config.syncType}"></td>
                        <td th:if="${isAdmin}">
                            <button class="action-btn edit-btn" th:data-id="${config.id}">编辑</button>
                            <button class="action-btn delete-btn" 
                                    th:data-id="${config.id}" 
                                    th:data-source-db="${config.sourceDb}" 
                                    th:data-source-table="${config.sourceTable}">删除</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 配置弹窗 -->
        <div class="modal fade" id="configModal" tabindex="-1" role="dialog" aria-labelledby="configModalLabel" aria-hidden="true" th:if="${isAdmin}">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="configModalLabel">表同步配置</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="configForm">
                            <input type="hidden" id="configId">
                            <div class="form-group">
                                <label for="modalSourceDb">源库名称 <span class="text-danger">*</span></label>
                                <select id="modalSourceDb" class="form-control select2" required>
                                    <option value="">请选择</option>
                                    <option th:each="db : ${sourceDbs}" th:value="${db}" th:text="${db}"></option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="modalSourceTable">源表名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="modalSourceTable" placeholder="请输入源表名称" required>
                            </div>
                            <div class="form-group">
                                <label for="modalTargetTable">目标表名称 <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="modalTargetTable" placeholder="请输入目标表名称" required>
                            </div>
                            <div class="form-group">
                                <label for="modalSyncType">接入方式 <span class="text-danger">*</span></label>
                                <select id="modalSyncType" class="form-control select2" required>
                                    <option value="">请选择</option>
                                    <option th:each="type : ${syncTypes}" th:value="${type}" th:text="${type}"></option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-primary" id="saveBtn">保存</button>
                    </div>
                </div>
            </div>
        </div>
    </section>
</body>
</html> 