<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(~{::title}, ~{::head}, ~{::content})}">
<head>
    <title>项目统计数据</title>

    <!-- 立即执行的脚本 - 定义全局函数 -->
    <script>
        // 全局函数定义，确保在HTML解析时就可用
        window.handleReportButtonClick = function(button) {
            console.log('全局handleReportButtonClick函数被调用');

            const projectName = button.getAttribute('data-project-name');
            console.log('项目名称：', projectName);

            if (!projectName) {
                console.error('项目名称为空');
                alert('项目名称不能为空');
                return;
            }

            window.viewAnalysisReport(projectName);
        };

        // 查看分析报告的全局函数 - 新窗口方式
        window.viewAnalysisReport = function(projectName) {
            console.log('开始查看项目分析报告：', projectName);

            // 构建报告页面URL
            const reportUrl = '/project-stats-data/analysis-report?projectName=' + encodeURIComponent(projectName);
            console.log('报告页面URL：', reportUrl);

            // 在新窗口/新标签页中打开报告页面
            const newWindow = window.open(reportUrl, '_blank', 'width=1400,height=900,scrollbars=yes,resizable=yes');

            if (!newWindow) {
                // 如果弹窗被阻止，提示用户
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'warning',
                        title: '弹窗被阻止',
                        text: '请允许弹窗或手动点击链接查看报告',
                        showCancelButton: true,
                        confirmButtonText: '手动打开',
                        cancelButtonText: '取消'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = reportUrl;
                        }
                    });
                } else {
                    const userConfirm = confirm('弹窗被阻止，是否在当前页面打开报告？');
                    if (userConfirm) {
                        window.location.href = reportUrl;
                    }
                }
            } else {
                console.log('报告页面已在新窗口中打开');
            }
        };
    </script>
    
    <style>
        .stats-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .page-header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .page-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .search-form {
            background: white;
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            align-items: flex-end;
            flex-wrap: wrap;
            margin-bottom: 0;
        }

        .form-group {
            flex: 1;
            min-width: 220px;
            max-width: 300px;
            margin-bottom: 15px;
            position: relative;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
            font-size: 14px;
            white-space: nowrap;
        }

        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 14px;
            transition: border-color 0.3s;
            box-sizing: border-box;
            height: 44px;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn-group {
            display: flex;
            gap: 10px;
            flex-shrink: 0;
            align-items: flex-end;
            margin-bottom: 15px;
        }

        /* 响应式布局优化 */
        @media (max-width: 1200px) {
            .form-group {
                min-width: 200px;
                max-width: 250px;
            }
        }

        @media (max-width: 992px) {
            .form-row {
                flex-direction: column;
                align-items: stretch;
            }

            .form-group {
                min-width: 100%;
                max-width: 100%;
                margin-bottom: 20px;
            }

            .btn-group {
                justify-content: center;
                margin-top: 10px;
            }
        }

        @media (max-width: 768px) {
            .btn-group {
                flex-direction: column;
                width: 100%;
            }

            .btn {
                width: 100%;
                justify-content: center;
            }
        }
        
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
            transform: translateY(-1px);
        }
        
        .stats-table-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
            overflow-x: auto;
        }

        .stats-table {
            width: 100%;
            border-collapse: collapse;
            min-width: 1000px;
        }

        .stats-table th {
            background: #f8f9fa;
            padding: 15px 12px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
            white-space: nowrap;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .stats-table td {
            padding: 15px 12px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: top;
        }

        .stats-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .project-name-cell {
            font-weight: 600;
            color: #495057;
            min-width: 120px;
            max-width: 180px;
            word-break: break-word;
        }

        .category-cell {
            max-width: 220px;
            min-width: 180px;
            white-space: pre-line;
            font-size: 13px;
            line-height: 1.5;
            word-break: break-word;
            overflow-wrap: break-word;
        }

        .number-cell {
            text-align: center;
            font-weight: 600;
            color: #495057;
            min-width: 80px;
        }

        .action-cell {
            text-align: center;
            white-space: nowrap;
            min-width: 100px;
        }
        
        .btn-view {
            background: #28a745;
            color: white;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 12px;
            text-decoration: none;
            transition: all 0.3s;
            border: none;
            outline: none;
        }

        .btn-view:hover {
            background: #218838;
            color: white;
            text-decoration: none;
            outline: none;
        }

        .btn-view:focus {
            outline: none;
            box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.25);
        }

        .btn-view:disabled {
            background: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
            outline: none;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4em;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-state h5 {
            margin-bottom: 10px;
            color: #495057;
        }

        /* 表格滚动条样式 */
        .stats-table-container::-webkit-scrollbar {
            height: 8px;
        }

        .stats-table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        .stats-table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        .stats-table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 表格行条纹效果 */
        .stats-table tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .stats-table tbody tr:nth-child(odd) {
            background-color: white;
        }

        .stats-table tbody tr:hover {
            background-color: #e3f2fd !important;
            transition: background-color 0.2s ease;
        }

        /* 分类统计文本样式优化 */
        .category-cell {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            border-left: 3px solid #dee2e6;
        }

        /* 数字单元格样式优化 */
        .number-cell {
            font-family: 'Arial', sans-serif;
            font-size: 16px;
            background-color: #fff;
        }

        /* 按钮悬停效果优化 */
        .btn-view {
            transition: all 0.3s ease;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .btn-view:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }
    </style>
</head>

<div th:fragment="content">
    <div class="stats-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1><i class="fas fa-chart-bar"></i> 项目统计数据</h1>
            <p>按项目维度展示汇总统计数据和分析报告</p>
        </div>

        <!-- 查询表单 -->
        <div class="search-form">
            <form th:action="@{/project-stats-data/list}" method="get">
                <div class="form-row">
                    <div class="form-group">
                        <label for="projectName">项目名称</label>
                        <input type="text" id="projectName" name="projectName" class="form-control" 
                               th:value="${projectName}" placeholder="支持模糊查询">
                    </div>
                    <div class="form-group">
                        <label for="onlineDateStart">上线日期开始</label>
                        <input type="date" id="onlineDateStart" name="onlineDateStart" class="form-control"
                               th:value="${onlineDateStartFormatted}">
                    </div>
                    <div class="form-group">
                        <label for="onlineDateEnd">上线日期结束</label>
                        <input type="date" id="onlineDateEnd" name="onlineDateEnd" class="form-control"
                               th:value="${onlineDateEndFormatted}">
                    </div>
                    <div class="btn-group">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> 查询
                        </button>
                        <a th:href="@{/project-stats-data/list}" class="btn btn-secondary">
                            <i class="fas fa-redo"></i> 重置
                        </a>
                    </div>
                </div>
            </form>
        </div>

        <!-- 统计数据表格 -->
        <div class="stats-table-container">
            <table class="stats-table">
                <thead>
                    <tr>
                        <th>项目名称</th>
                        <th>总问题数量</th>
                        <th>开发问题数量</th>
                        <th>测试问题数量</th>
                        <th>开发问题分类统计</th>
                        <th>测试问题分类统计</th>
                        <th>项目分析报告</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:if="${#lists.isEmpty(projectStatsDataList)}">
                        <td colspan="7">
                            <div class="empty-state">
                                <i class="fas fa-inbox"></i>
                                <h5>暂无数据</h5>
                                <p>请调整筛选条件重新查询</p>
                            </div>
                        </td>
                    </tr>
                    <tr th:each="data : ${projectStatsDataList}">
                        <td class="project-name-cell" th:text="${data.projectName}"></td>
                        <td class="number-cell" th:text="${data.totalIssues}"></td>
                        <td class="number-cell" th:text="${data.devIssues}"></td>
                        <td class="number-cell" th:text="${data.testIssues}"></td>
                        <td class="category-cell" th:text="${data.devIssueCategories}"></td>
                        <td class="category-cell" th:text="${data.testIssueCategories}"></td>
                        <td class="action-cell">
                            <button th:if="${data.hasAnalysisReport}"
                                    class="btn-view view-report-btn"
                                    type="button"
                                    th:attr="data-project-name=${data.projectName}"
                                    onclick="window.handleReportButtonClick(this)">
                                查看报告
                            </button>
                            <button th:unless="${data.hasAnalysisReport}"
                                    class="btn-view"
                                    type="button"
                                    disabled
                                    title="暂无分析报告">
                                暂无报告
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>


</div>

<script>
    // 页面初始化完成后的调试信息
    window.addEventListener('load', function() {
        console.log('页面完全加载完成');

        // 查找所有查看报告按钮
        const reportButtons = document.querySelectorAll('.view-report-btn');
        console.log('找到查看报告按钮数量：', reportButtons.length);

        reportButtons.forEach(function(button, index) {
            console.log('按钮' + (index + 1) + '：', button.textContent, '项目名称：', button.getAttribute('data-project-name'));
        });
    });
</script>

</html>
