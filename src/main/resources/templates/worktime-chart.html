<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::content}
      )}">
<head>
    <title>研发工时分布统计</title>
    <style>
        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 20px;
            padding: 20px;
        }
        .chart {
            height: 400px;
            background: #fff;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .total-chart {
            grid-column: 1 / -1;
            height: 500px;
        }
    </style>
</head>
<body>
    <div th:fragment="content">
        <div class="filter-container">
            <form th:action="@{/worktime/chart}" method="get" id="filterForm">
                <!-- 筛选条件表单 -->
                <div class="filter-item">
                    <label class="filter-label">团队：</label>
                    <select name="selectedTeam" class="select2-single">
                        <option value="">全部团队</option>
                        <option th:each="team : ${teams}" 
                                th:value="${team}" 
                                th:text="${team}"
                                th:selected="${team == selectedTeam}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">用户名：</label>
                    <select name="selectedUsers" multiple class="select2-multi" data-placeholder="选择用户...">
                        <option th:each="username : ${usernames}" 
                                th:value="${username}" 
                                th:text="${username}"
                                th:selected="${selectedUsers != null && selectedUsers.contains(username)}">
                        </option>
                    </select>
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">起始月份：</label>
                    <input type="month" name="startMonth" th:value="${startMonth}">
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">结束月份：</label>
                    <input type="month" name="endMonth" th:value="${endMonth}">
                </div>
                
                <div class="filter-item">
                    <label class="filter-label">&nbsp;</label>
                    <button type="submit">筛选</button>
                    <button type="button" onclick="clearSelection()">重置</button>
                </div>
            </form>
        </div>
        
        <div class="charts-container">
            <div id="totalChart" class="chart total-chart"></div>
            <div th:each="username : ${selectedUsers ?: usernames}" 
                 th:id="'userChart_' + ${username.replace(' ', '_')}"
                 class="chart">
            </div>
        </div>

        <script th:inline="javascript">
            // 初始化Select2和事件监听代码
            $(document).ready(function() {
                // 初始化用户多选
                $('.select2-multi').select2({
                    allowClear: true,
                    closeOnSelect: false,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的用户";
                        }
                    }
                }).on('change', function() {  // 添加用户选择变更事件
                    submitForm();
                });
                
                // 初始化团队选择
                $('.select2-single').select2({
                    allowClear: true,
                    language: {
                        noResults: function() {
                            return "没有找到匹配的团队";
                        }
                    }
                });

                // 监听团队选择变化
                $('select[name="selectedTeam"]').on('change', function() {
                    var selectedTeam = $(this).val();
                    var userSelect = $('.select2-multi');
                    
                    if (selectedTeam) {
                        $.get('/worktime/users-by-team', { team: selectedTeam }, function(users) {
                            var currentSelected = userSelect.val() || [];
                            userSelect.empty();
                            users.forEach(function(username) {
                                var newOption = new Option(username, username, false, false);
                                userSelect.append(newOption);
                            });
                            // 恢复之前选中的用户（如果在新的用户列表中存在）
                            var validSelected = currentSelected.filter(user => users.includes(user));
                            userSelect.val(validSelected).trigger('change');
                        });
                    } else {
                        // 如果清除了团队选择，自动选择CRM团队
                        $(this).val('CRM').trigger('change');
                    }
                });

                // 月份变化监听
                $('input[type="month"]').on('change', function() {
                    submitForm();
                });
            });

            // 修改清除选择函数
            function clearSelection() {
                $('select[name="selectedTeam"]').val('CRM').trigger('change');
                $('.select2-multi').val(null).trigger('change');
                $('input[type="month"]').val('');
                submitForm();
            }

            // 添加提交表单函数
            function submitForm() {
                document.getElementById('filterForm').submit();
            }

            // 获取后端数据
            var workTimes = /*[[${workTimes}]]*/ [];
            var selectedUsers = /*[[${selectedUsers}]]*/ [];
            var allUsers = /*[[${usernames}]]*/ [];

            // 数据处理函数
            function processWorkTimeData(data, user = null) {
                var typeMap = new Map();
                
                // 筛选数据
                var filteredData = user ? 
                    data.filter(item => item.owner === user) : 
                    data;
                
                // 统计各类型工时
                filteredData.forEach(item => {
                    var current = typeMap.get(item.taskType) || 0;
                    typeMap.set(item.taskType, current + item.effort);
                });
                
                // 计算总工时
                var total = Array.from(typeMap.values()).reduce((a, b) => a + b, 0);
                
                // 转换为百分比并处理小于2%的情况
                var result = [];
                var others = 0;
                
                typeMap.forEach((value, key) => {
                    var percentage = (value / total) * 100;
                    if (percentage >= 2) {
                        result.push({
                            name: key,
                            value: percentage.toFixed(2),
                            actualHours: value
                        });
                    } else {
                        others += value;
                    }
                });
                
                // 如果有其他类别，添加到结果中
                if (others > 0) {
                    result.push({
                        name: '其他',
                        value: ((others / total) * 100).toFixed(2),
                        actualHours: others
                    });
                }
                
                return result;
            }

            // 创建饼图的通用函数
            function createPieChart(elementId, data, title) {
                var chart = echarts.init(document.getElementById(elementId));
                
                var option = {
                    title: {
                        text: title,
                        left: 'center'
                    },
                    tooltip: {
                        trigger: 'item',
                        formatter: function(params) {
                            return params.name + '<br/>' +
                                   '占比：' + params.value + '%<br/>' +
                                   '工时：' + params.data.actualHours.toFixed(2) + '小时';
                        }
                    },
                    legend: {
                        orient: 'vertical',
                        right: 10,
                        top: 'middle'
                    },
                    series: [{
                        name: '工时分布',
                        type: 'pie',
                        radius: '50%',
                        data: data.map(item => ({
                            name: item.name,
                            value: item.value,
                            actualHours: item.actualHours
                        })),
                        label: {
                            show: true,
                            formatter: '{b}: {d}%',
                            position: 'outside'
                        },
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            },
                            label: {
                                show: true,
                                formatter: '{b}: {d}%',
                                fontSize: 14
                            }
                        }
                    }]
                };
                
                chart.setOption(option);
                return chart;
            }

            // 创建所有图表
            var charts = [];
            
            // 创建总体分布图
            var totalData = processWorkTimeData(workTimes);
            charts.push(createPieChart('totalChart', totalData, '总体工时分布'));

            // 创建每个用户的分布图
            var users = selectedUsers && selectedUsers.length > 0 ? selectedUsers : allUsers;
            users.forEach(user => {
                var userData = processWorkTimeData(workTimes, user);
                var elementId = 'userChart_' + user.replace(' ', '_');
                charts.push(createPieChart(elementId, userData, user + ' 工时分布'));
            });

            // 响应式调整
            window.addEventListener('resize', function() {
                charts.forEach(chart => chart.resize());
            });
        </script>
    </div>
</body>
</html> 