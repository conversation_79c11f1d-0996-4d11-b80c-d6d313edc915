<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(~{::title}, ~{::head}, ~{::content})}">
<head>
    <title>项目验收数据分析</title>
    
    <style>
        /* 页面整体样式 */
        .analysis-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
            min-height: 100vh;
        }

        /* 页面标题样式 */
        .page-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .page-header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .page-header h1 i {
            margin-right: 15px;
            font-size: 32px;
        }

        .page-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }

        /* 筛选条件样式 */
        .filter-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .filter-title {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .filter-title i {
            font-size: 20px;
            color: #28a745;
            margin-right: 10px;
        }

        .filter-title h4 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }

        .filter-row {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }

        .filter-group label {
            font-weight: 500;
            color: #555;
            margin-bottom: 5px;
            font-size: 14px;
        }

        .filter-group select,
        .filter-group input {
            padding: 8px 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-group select:focus,
        .filter-group input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
        }

        .filter-actions {
            display: flex;
            gap: 10px;
            align-items: end;
        }

        .btn-search {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }

        .btn-reset {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-reset:hover {
            background: #5a6268;
        }

        /* 图表容器样式 */
        .charts-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 30px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .chart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.12);
        }

        .chart-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .chart-header i {
            font-size: 20px;
            margin-right: 10px;
        }

        .chart-header h5 {
            margin: 0;
            color: #333;
            font-weight: 600;
            font-size: 18px;
        }

        .chart-content {
            height: 400px;
            position: relative;
        }

        /* 详细统计表格样式 */
        .detail-section {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #e9ecef;
        }

        .detail-header {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .detail-header i {
            font-size: 20px;
            color: #dc3545;
            margin-right: 10px;
        }

        .detail-header h4 {
            margin: 0;
            color: #333;
            font-weight: 600;
        }

        .detail-tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 2px solid #f8f9fa;
        }

        .detail-tab {
            padding: 10px 20px;
            cursor: pointer;
            border: none;
            background: none;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .detail-tab.active {
            color: #007bff;
            border-bottom-color: #007bff;
        }

        .detail-content {
            display: none;
        }

        .detail-content.active {
            display: block;
        }

        .detail-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .detail-table th,
        .detail-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }

        .detail-table tr:hover {
            background-color: #f8f9fa;
        }

        /* 加载状态样式 */
        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #6c757d;
        }

        .loading i {
            font-size: 24px;
            margin-right: 10px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 空数据状态样式 */
        .empty-state {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            height: 200px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 48px;
            margin-bottom: 15px;
            opacity: 0.5;
        }

        .empty-state h5 {
            margin: 0 0 10px 0;
            font-weight: 500;
        }

        .empty-state p {
            margin: 0;
            font-size: 14px;
            opacity: 0.8;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .analysis-container {
                padding: 10px;
            }
            
            .charts-container {
                grid-template-columns: 1fr;
            }
            
            .filter-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .filter-group {
                min-width: auto;
            }
        }
    </style>
</head>

<body>
<div th:fragment="content">
    <div class="analysis-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1>
                <i class="fas fa-chart-line"></i>
                项目验收数据分析
            </h1>
            <p>通过可视化图表分析项目质量状况，识别问题类型分布，评估团队成员工作质量</p>
        </div>

        <!-- 筛选条件 -->
        <div class="filter-section">
            <div class="filter-title">
                <i class="fas fa-filter"></i>
                <h4>筛选条件</h4>
            </div>
            <form id="filterForm">
                <div class="filter-row">
                    <div class="filter-group">
                        <label for="projectName">项目名称</label>
                        <select id="projectName" name="projectName">
                            <option value="">全部项目</option>
                            <option th:each="project : ${projectNames}" 
                                    th:value="${project}" 
                                    th:text="${project}"
                                    th:selected="${project == selectedProjectName}"></option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <label for="grayDateStart">灰度日期开始</label>
                        <input type="date" id="grayDateStart" name="grayDateStart" th:value="${selectedGrayDateStart}">
                    </div>
                    <div class="filter-group">
                        <label for="grayDateEnd">灰度日期结束</label>
                        <input type="date" id="grayDateEnd" name="grayDateEnd" th:value="${selectedGrayDateEnd}">
                    </div>
                    <div class="filter-group">
                        <label for="onlineDateStart">上线日期开始</label>
                        <input type="date" id="onlineDateStart" name="onlineDateStart" th:value="${selectedOnlineDateStart}">
                    </div>
                    <div class="filter-group">
                        <label for="onlineDateEnd">上线日期结束</label>
                        <input type="date" id="onlineDateEnd" name="onlineDateEnd" th:value="${selectedOnlineDateEnd}">
                    </div>
                    <div class="filter-actions">
                        <button type="button" class="btn-search" onclick="loadAllCharts()">
                            <i class="fas fa-search"></i>
                            查询
                        </button>
                        <button type="button" class="btn-reset" onclick="resetFilters()">
                            <i class="fas fa-undo"></i>
                            重置
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- 统计图表 -->
        <div class="charts-container">
            <!-- 开发问题分类统计图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <i class="fas fa-bug" style="color: #dc3545;"></i>
                    <h5>开发问题分类统计</h5>
                </div>
                <div class="chart-content" id="devIssueCategoryChart"></div>
            </div>

            <!-- 测试问题分类统计图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <i class="fas fa-vial" style="color: #28a745;"></i>
                    <h5>测试问题分类统计</h5>
                </div>
                <div class="chart-content" id="testIssueCategoryChart"></div>
            </div>

            <!-- 测试人员Bug数统计图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <i class="fas fa-user-check" style="color: #007bff;"></i>
                    <h5>测试人员Bug数统计</h5>
                </div>
                <div class="chart-content" id="testerBugChart"></div>
            </div>

            <!-- 开发人员Bug数统计图 -->
            <div class="chart-card">
                <div class="chart-header">
                    <i class="fas fa-code" style="color: #fd7e14;"></i>
                    <h5>开发人员Bug数统计</h5>
                </div>
                <div class="chart-content" id="developerBugChart"></div>
            </div>
        </div>

        <!-- 详细统计表格 -->
        <div class="detail-section">
            <div class="detail-header">
                <i class="fas fa-table"></i>
                <h4>详细统计数据</h4>
            </div>
            <div class="detail-tabs">
                <button class="detail-tab active" onclick="showDetailTab('developer')">开发人员问题分类详细统计</button>
                <button class="detail-tab" onclick="showDetailTab('tester')">测试人员问题分类详细统计</button>
            </div>
            <div id="developerDetail" class="detail-content active">
                <div id="developerDetailChart" style="height: 500px;"></div>
            </div>
            <div id="testerDetail" class="detail-content">
                <div id="testerDetailChart" style="height: 500px;"></div>
            </div>
        </div>
    </div>

    <script>
        /**
         * 项目验收数据分析页面JavaScript
         * <AUTHOR>
         * @date 2025-07-10 19:30:35
         */

        // 图表实例
        let devIssueCategoryChart = null;
        let testIssueCategoryChart = null;
        let testerBugChart = null;
        let developerBugChart = null;
        let developerDetailChart = null;
        let testerDetailChart = null;

        // 页面加载完成后初始化
        $(document).ready(function() {
            console.log('=== 页面加载完成 ===');
            console.log('jQuery版本:', $.fn.jquery);
            console.log('ECharts是否加载:', typeof echarts !== 'undefined');
            console.log('SweetAlert2是否加载:', typeof Swal !== 'undefined');

            initializeCharts();

            // 延迟加载图表数据，确保初始化完成
            setTimeout(() => {
                console.log('开始加载图表数据...');
                loadAllCharts();
            }, 1000);
        });

        /**
         * 初始化所有图表
         */
        function initializeCharts() {
            console.log('开始初始化图表...');

            try {
                // 检查ECharts是否加载
                if (typeof echarts === 'undefined') {
                    console.error('ECharts未加载！');
                    return;
                }

                // 初始化开发问题分类统计图
                const devElement = document.getElementById('devIssueCategoryChart');
                if (devElement) {
                    devIssueCategoryChart = echarts.init(devElement);
                    console.log('开发问题分类统计图初始化成功');
                } else {
                    console.error('找不到devIssueCategoryChart元素');
                }

                // 初始化测试问题分类统计图
                const testElement = document.getElementById('testIssueCategoryChart');
                if (testElement) {
                    testIssueCategoryChart = echarts.init(testElement);
                    console.log('测试问题分类统计图初始化成功');
                } else {
                    console.error('找不到testIssueCategoryChart元素');
                }

                // 初始化测试人员Bug数统计图
                const testerElement = document.getElementById('testerBugChart');
                if (testerElement) {
                    testerBugChart = echarts.init(testerElement);
                    console.log('测试人员Bug数统计图初始化成功');
                } else {
                    console.error('找不到testerBugChart元素');
                }

                // 初始化开发人员Bug数统计图
                const developerElement = document.getElementById('developerBugChart');
                if (developerElement) {
                    developerBugChart = echarts.init(developerElement);
                    console.log('开发人员Bug数统计图初始化成功');
                } else {
                    console.error('找不到developerBugChart元素');
                }

                // 初始化开发人员详细统计图
                const devDetailElement = document.getElementById('developerDetailChart');
                if (devDetailElement) {
                    developerDetailChart = echarts.init(devDetailElement);
                    console.log('开发人员详细统计图初始化成功');
                } else {
                    console.error('找不到developerDetailChart元素');
                }

                // 初始化测试人员详细统计图
                const testerDetailElement = document.getElementById('testerDetailChart');
                if (testerDetailElement) {
                    testerDetailChart = echarts.init(testerDetailElement);
                    console.log('测试人员详细统计图初始化成功');
                } else {
                    console.error('找不到testerDetailChart元素');
                }

                // 窗口大小改变时重新调整图表大小
                window.addEventListener('resize', function() {
                    devIssueCategoryChart && devIssueCategoryChart.resize();
                    testIssueCategoryChart && testIssueCategoryChart.resize();
                    testerBugChart && testerBugChart.resize();
                    developerBugChart && developerBugChart.resize();
                    developerDetailChart && developerDetailChart.resize();
                    testerDetailChart && testerDetailChart.resize();
                });

                console.log('所有图表初始化完成');
            } catch (error) {
                console.error('图表初始化失败：', error);
            }
        }

        /**
         * 加载所有图表数据
         */
        function loadAllCharts() {
            console.log('=== 开始加载图表数据 ===');

            // 检查图表是否已初始化
            if (!devIssueCategoryChart || !testIssueCategoryChart || !testerBugChart ||
                !developerBugChart || !developerDetailChart || !testerDetailChart) {
                console.error('图表未正确初始化，重新初始化...');
                initializeCharts();

                // 延迟执行，确保图表初始化完成
                setTimeout(() => {
                    loadAllCharts();
                }, 500);
                return;
            }

            const params = getFilterParams();
            console.log('筛选参数：', params);

            // 显示加载状态
            showLoading();

            // 并行加载所有图表数据
            console.log('开始并行加载6个图表的数据...');
            Promise.all([
                loadDevIssueCategoryChart(params),
                loadTestIssueCategoryChart(params),
                loadTesterBugChart(params),
                loadDeveloperBugChart(params),
                loadDeveloperDetailChart(params),
                loadTesterDetailChart(params)
            ]).then(() => {
                console.log('=== 所有图表数据加载完成 ===');
            }).catch(error => {
                console.error('=== 加载图表数据失败 ===', error);
                if (typeof Swal !== 'undefined') {
                    Swal.fire({
                        icon: 'error',
                        title: '加载失败',
                        text: '加载图表数据失败，请稍后重试'
                    });
                } else {
                    alert('加载图表数据失败，请稍后重试');
                }
            });
        }

        /**
         * 获取筛选参数
         */
        function getFilterParams() {
            return {
                projectName: $('#projectName').val(),
                grayDateStart: $('#grayDateStart').val(),
                grayDateEnd: $('#grayDateEnd').val(),
                onlineDateStart: $('#onlineDateStart').val(),
                onlineDateEnd: $('#onlineDateEnd').val()
            };
        }

        /**
         * 显示加载状态
         */
        function showLoading() {
            console.log('显示加载状态...');

            // 使用ECharts的showLoading方法而不是直接修改HTML
            if (devIssueCategoryChart) {
                devIssueCategoryChart.showLoading();
            }
            if (testIssueCategoryChart) {
                testIssueCategoryChart.showLoading();
            }
            if (testerBugChart) {
                testerBugChart.showLoading();
            }
            if (developerBugChart) {
                developerBugChart.showLoading();
            }
            if (developerDetailChart) {
                developerDetailChart.showLoading();
            }
            if (testerDetailChart) {
                testerDetailChart.showLoading();
            }
        }

        /**
         * 加载开发问题分类统计图
         */
        function loadDevIssueCategoryChart(params) {
            console.log('正在加载开发问题分类统计数据...');
            return $.get('/project-acceptance/api/dev-issue-category-stats', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const chartData = Object.keys(data).map(key => ({
                            name: key,
                            value: data[key]
                        }));

                        const option = {
                            title: {
                                text: '开发问题分类分布',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                                top: 'middle'
                            },
                            series: [{
                                name: '问题数量',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                center: ['60%', '50%'],
                                data: chartData,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                },
                                label: {
                                    formatter: '{b}: {c}'
                                }
                            }]
                        };

                        devIssueCategoryChart.hideLoading();
                        devIssueCategoryChart.setOption(option);
                        console.log('开发问题分类统计图数据加载成功');
                    } else {
                        devIssueCategoryChart.hideLoading();
                        showEmptyState('devIssueCategoryChart', '暂无开发问题分类数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('开发问题分类统计数据加载失败：', error);
                    devIssueCategoryChart.hideLoading();
                    showEmptyState('devIssueCategoryChart', '加载开发问题分类数据失败');
                });
        }

        /**
         * 加载测试问题分类统计图
         */
        function loadTestIssueCategoryChart(params) {
            return $.get('/project-acceptance/api/test-issue-category-stats', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const chartData = Object.keys(data).map(key => ({
                            name: key,
                            value: data[key]
                        }));

                        const option = {
                            title: {
                                text: '测试问题分类分布',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'item',
                                formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            legend: {
                                orient: 'vertical',
                                left: 'left',
                                top: 'middle'
                            },
                            series: [{
                                name: '问题数量',
                                type: 'pie',
                                radius: ['40%', '70%'],
                                center: ['60%', '50%'],
                                data: chartData,
                                emphasis: {
                                    itemStyle: {
                                        shadowBlur: 10,
                                        shadowOffsetX: 0,
                                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                                    }
                                },
                                label: {
                                    formatter: '{b}: {c}'
                                }
                            }]
                        };

                        testIssueCategoryChart.hideLoading();
                        testIssueCategoryChart.setOption(option);
                        console.log('测试问题分类统计图数据加载成功');
                    } else {
                        testIssueCategoryChart.hideLoading();
                        showEmptyState('testIssueCategoryChart', '暂无测试问题分类数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('测试问题分类统计数据加载失败：', error);
                    testIssueCategoryChart.hideLoading();
                    showEmptyState('testIssueCategoryChart', '加载测试问题分类数据失败');
                });
        }

        /**
         * 加载测试人员Bug数统计图
         */
        function loadTesterBugChart(params) {
            return $.get('/project-acceptance/api/tester-bug-stats', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const names = Object.keys(data);
                        const values = names.map(name => data[name]);

                        const option = {
                            title: {
                                text: '测试人员Bug数排行',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: names,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: [{
                                name: 'Bug数量',
                                type: 'bar',
                                data: values,
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#83bff6'},
                                        {offset: 0.5, color: '#188df0'},
                                        {offset: 1, color: '#188df0'}
                                    ])
                                },
                                emphasis: {
                                    itemStyle: {
                                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                            {offset: 0, color: '#2378f7'},
                                            {offset: 0.7, color: '#2378f7'},
                                            {offset: 1, color: '#83bff6'}
                                        ])
                                    }
                                }
                            }]
                        };

                        testerBugChart.hideLoading();
                        testerBugChart.setOption(option);
                        console.log('测试人员Bug数统计图数据加载成功');
                    } else {
                        testerBugChart.hideLoading();
                        showEmptyState('testerBugChart', '暂无测试人员Bug数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('测试人员Bug数统计数据加载失败：', error);
                    testerBugChart.hideLoading();
                    showEmptyState('testerBugChart', '加载测试人员Bug数据失败');
                });
        }

        /**
         * 加载开发人员Bug数统计图
         */
        function loadDeveloperBugChart(params) {
            return $.get('/project-acceptance/api/developer-bug-stats', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const names = Object.keys(data);
                        const values = names.map(name => data[name]);

                        const option = {
                            title: {
                                text: '开发人员Bug数排行',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: names,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: [{
                                name: 'Bug数量',
                                type: 'bar',
                                data: values,
                                itemStyle: {
                                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                        {offset: 0, color: '#ffecd2'},
                                        {offset: 0.5, color: '#fcb69f'},
                                        {offset: 1, color: '#fd7e14'}
                                    ])
                                },
                                emphasis: {
                                    itemStyle: {
                                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                            {offset: 0, color: '#fd7e14'},
                                            {offset: 0.7, color: '#fcb69f'},
                                            {offset: 1, color: '#ffecd2'}
                                        ])
                                    }
                                }
                            }]
                        };

                        developerBugChart.hideLoading();
                        developerBugChart.setOption(option);
                        console.log('开发人员Bug数统计图数据加载成功');
                    } else {
                        developerBugChart.hideLoading();
                        showEmptyState('developerBugChart', '暂无开发人员Bug数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('开发人员Bug数统计数据加载失败：', error);
                    developerBugChart.hideLoading();
                    showEmptyState('developerBugChart', '加载开发人员Bug数据失败');
                });
        }

        /**
         * 加载开发人员问题分类详细统计图
         */
        function loadDeveloperDetailChart(params) {
            return $.get('/project-acceptance/api/developer-issue-detail-stats', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const developers = Object.keys(data);
                        const categories = [...new Set(Object.values(data).flatMap(dev => Object.keys(dev)))];

                        const seriesData = categories.map(category => ({
                            name: category,
                            type: 'bar',
                            stack: 'total',
                            data: developers.map(dev => data[dev][category] || 0)
                        }));

                        const option = {
                            title: {
                                text: '开发人员问题分类详细统计',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            legend: {
                                data: categories,
                                top: 30
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                top: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: developers,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: seriesData
                        };

                        developerDetailChart.hideLoading();
                        developerDetailChart.setOption(option);
                        console.log('开发人员详细统计图数据加载成功');
                    } else {
                        developerDetailChart.hideLoading();
                        showEmptyState('developerDetailChart', '暂无开发人员详细统计数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('开发人员详细统计数据加载失败：', error);
                    developerDetailChart.hideLoading();
                    showEmptyState('developerDetailChart', '加载开发人员详细统计数据失败');
                });
        }

        /**
         * 加载测试人员问题分类详细统计图
         */
        function loadTesterDetailChart(params) {
            return $.get('/project-acceptance/api/tester-issue-detail-stats', params)
                .done(function(response) {
                    if (response.success) {
                        const data = response.data;
                        const testers = Object.keys(data);
                        const categories = [...new Set(Object.values(data).flatMap(tester => Object.keys(tester)))];

                        const seriesData = categories.map(category => ({
                            name: category,
                            type: 'bar',
                            stack: 'total',
                            data: testers.map(tester => data[tester][category] || 0)
                        }));

                        const option = {
                            title: {
                                text: '测试人员问题分类详细统计',
                                left: 'center',
                                textStyle: {
                                    fontSize: 16,
                                    fontWeight: 'bold'
                                }
                            },
                            tooltip: {
                                trigger: 'axis',
                                axisPointer: {
                                    type: 'shadow'
                                }
                            },
                            legend: {
                                data: categories,
                                top: 30
                            },
                            grid: {
                                left: '3%',
                                right: '4%',
                                bottom: '3%',
                                top: '15%',
                                containLabel: true
                            },
                            xAxis: {
                                type: 'category',
                                data: testers,
                                axisLabel: {
                                    rotate: 45,
                                    interval: 0
                                }
                            },
                            yAxis: {
                                type: 'value'
                            },
                            series: seriesData
                        };

                        testerDetailChart.hideLoading();
                        testerDetailChart.setOption(option);
                        console.log('测试人员详细统计图数据加载成功');
                    } else {
                        testerDetailChart.hideLoading();
                        showEmptyState('testerDetailChart', '暂无测试人员详细统计数据');
                    }
                })
                .fail(function(xhr, status, error) {
                    console.error('测试人员详细统计数据加载失败：', error);
                    testerDetailChart.hideLoading();
                    showEmptyState('testerDetailChart', '加载测试人员详细统计数据失败');
                });
        }

        /**
         * 显示空数据状态
         */
        function showEmptyState(containerId, message) {
            console.log('显示空数据状态：', containerId, message);

            // 获取对应的图表实例
            let chart = null;
            switch(containerId) {
                case 'devIssueCategoryChart':
                    chart = devIssueCategoryChart;
                    break;
                case 'testIssueCategoryChart':
                    chart = testIssueCategoryChart;
                    break;
                case 'testerBugChart':
                    chart = testerBugChart;
                    break;
                case 'developerBugChart':
                    chart = developerBugChart;
                    break;
                case 'developerDetailChart':
                    chart = developerDetailChart;
                    break;
                case 'testerDetailChart':
                    chart = testerDetailChart;
                    break;
            }

            if (chart) {
                // 使用ECharts显示空数据
                const emptyOption = {
                    title: {
                        text: message,
                        left: 'center',
                        top: 'middle',
                        textStyle: {
                            color: '#999',
                            fontSize: 16
                        }
                    }
                };
                chart.setOption(emptyOption);
            }
        }

        /**
         * 重置筛选条件
         */
        function resetFilters() {
            $('#projectName').val('');
            $('#grayDateStart').val('');
            $('#grayDateEnd').val('');
            $('#onlineDateStart').val('');
            $('#onlineDateEnd').val('');
            loadAllCharts();
        }

        /**
         * 切换详细统计标签页
         */
        function showDetailTab(tabName) {
            // 切换标签页激活状态
            $('.detail-tab').removeClass('active');
            $(`.detail-tab:contains('${tabName === 'developer' ? '开发人员' : '测试人员'}')`).addClass('active');

            // 切换内容显示
            $('.detail-content').removeClass('active');
            $(`#${tabName}Detail`).addClass('active');

            // 重新调整图表大小
            setTimeout(() => {
                if (tabName === 'developer') {
                    developerDetailChart && developerDetailChart.resize();
                } else {
                    testerDetailChart && testerDetailChart.resize();
                }
            }, 100);
        }

        // 键盘事件处理
        $(document).keydown(function(e) {
            // 按Enter键执行查询
            if (e.keyCode === 13) {
                loadAllCharts();
            }
        });
    </script>
</div>
</body>
</html>
