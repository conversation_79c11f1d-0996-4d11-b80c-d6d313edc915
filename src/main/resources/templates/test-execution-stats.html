<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org"
      th:replace="~{layout/base :: html(
        title=~{::title},
        head=~{::head},
        content=~{::section}
      )}">
<head>
    <title>自动化测试执行统计</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/zh.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/jquery.dataTables.min.css">
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <style>
        .filter-container {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            align-items: flex-end;
            margin: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        .filter-item {
            margin-bottom: 10px;
        }
        .filter-label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #495057;
        }
        .date-range-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }
        .date-input {
            width: 120px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .table-container {
            padding: 20px;
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            margin: 20px;
            overflow-x: auto;
        }
        .stats-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            text-align: center;
            border: 1px solid #e0e0e0;
        }
        .stats-table th, .stats-table td {
            padding: 12px 15px;
            border: 1px solid #e0e0e0;
            color: #333;
        }
        .stats-table th {
            background-color: #f0f2f5;
            font-weight: 600;
            cursor: pointer;
            position: relative;
            border-bottom: 2px solid #ddd;
        }
        .stats-table th:hover {
            background-color: #e5e8ed;
        }
        .stats-table tbody tr:nth-child(even) {
            background-color: #f9fafb;
        }
        .stats-table tbody tr:hover {
            background-color: #f0f7ff;
            transition: background-color 0.2s ease;
        }
        .no-data {
            text-align: center;
            padding: 30px;
            color: #888;
            font-style: italic;
        }
        .pass-rate-high {
            color: #2eb82e;
            font-weight: bold;
        }
        .pass-rate-medium {
            color: #ff9900;
            font-weight: bold;
        }
        .pass-rate-low {
            color: #ff4d4d;
            font-weight: bold;
        }
        #searchBtn {
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .select2-container--default .select2-selection--single {
            height: 38px;
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .select2-container--default .select2-selection--single .select2-selection__arrow {
            height: 36px;
        }
    </style>
    <script th:inline="javascript">
        $(document).ready(function() {
            // 初始化日期选择器
            flatpickr("#startDate", {
                dateFormat: "Ymd",
                locale: "zh",
                allowInput: true
            });
            
            flatpickr("#endDate", {
                dateFormat: "Ymd",
                locale: "zh",
                allowInput: true
            });
            
            // 初始化Select2
            $('.select2').select2({
                width: '100%',
                placeholder: '请选择'
            });
            
            // 初始化DataTable，添加排序功能
            $('#statsTable').DataTable({
                paging: false,
                searching: false,
                info: false,
                ordering: false,
                language: {
                    emptyTable: "暂无数据"
                },
                columnDefs: [
                    { className: "dt-center", targets: "_all" }
                ],
                drawCallback: function() {
                    // 为通过率添加颜色标识
                    $('.stats-table tbody tr').each(function() {
                        var passRateCell = $(this).find('td:eq(3)');
                        var passRateText = passRateCell.text();
                        if (passRateText && passRateText !== '') {
                            var passRate = parseFloat(passRateText);
                            if (passRate >= 90) {
                                passRateCell.addClass('pass-rate-high');
                            } else if (passRate >= 70) {
                                passRateCell.addClass('pass-rate-medium');
                            } else {
                                passRateCell.addClass('pass-rate-low');
                            }
                        }
                    });
                }
            });
            
            // 查询按钮点击事件
            $('#searchBtn').click(function() {
                var startDate = $('#startDate').val();
                var endDate = $('#endDate').val();
                var appName = $('#appName').val();
                var executeEnv = $('#executeEnv').val();

                // 构建查询URL
                var url = '/test-execution/stats?';
                if (startDate) url += 'startDate=' + startDate + '&';
                if (endDate) url += 'endDate=' + endDate + '&';
                if (appName) url += 'appName=' + encodeURIComponent(appName) + '&';
                if (executeEnv) url += 'executeEnv=' + encodeURIComponent(executeEnv);
                
                // 跳转到查询URL
                window.location.href = url;
            });
            
            // 响应式调整
            $(window).resize(function() {
                $('#statsTable').DataTable().columns.adjust();
            });
        });
    </script>
</head>
<body>
    <section>
        <h2>自动化测试执行统计</h2>
        
        <!-- 过滤条件 -->
        <div class="filter-container">
            <div class="filter-item">
                <label class="filter-label">日期范围</label>
                <div class="date-range-container">
                    <input type="text" id="startDate" class="date-input" th:value="${queryParam.startDate}" placeholder="开始日期">
                    <span>至</span>
                    <input type="text" id="endDate" class="date-input" th:value="${queryParam.endDate}" placeholder="结束日期">
                </div>
            </div>
            
            <div class="filter-item">
                <label class="filter-label">应用名</label>
                <select id="appName" class="select2">
                    <option th:each="app : ${appNames}"
                            th:value="${app}" 
                            th:text="${app}"
                            th:selected="${app == queryParam.appName}"></option>
                </select>
            </div>
            
            <div class="filter-item">
                <label class="filter-label">执行环境</label>
                <select id="executeEnv" class="select2">
                    <option th:each="env : ${executeEnvs}"
                            th:value="${env}" 
                            th:text="${env}"
                            th:selected="${env == queryParam.executeEnv}"></option>
                </select>
            </div>
            
            <div class="filter-item">
                <button id="searchBtn">查询</button>
            </div>
        </div>
        
        <!-- 统计表格 -->
        <div class="table-container">
            <table id="statsTable" class="stats-table">
                <thead>
                    <tr>
                        <th>执行日期</th>
                        <th>应用名</th>
                        <th>测试集耗时加总(分钟)</th>
                        <th>案例平均通过率</th>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>执行环境</th>
                    </tr>
                </thead>
                <tbody>
                    <tr th:if="${stats.empty}">
                        <td colspan="7" class="no-data">暂无数据</td>
                    </tr>
                    <tr th:each="stat : ${stats}">
                        <td th:text="${stat.executeDate}"></td>
                        <td th:text="${stat.appName}"></td>
                        <td th:text="${#numbers.formatDecimal(stat.totalDuration, 1, 2)}"></td>
                        <td th:text="${stat.avgPassRate}"></td>
                        <td th:text="${#dates.format(stat.startTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                        <td th:text="${#dates.format(stat.endTime, 'yyyy-MM-dd HH:mm:ss')}"></td>
                        <td th:text="${stat.executeEnv}"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </section>
</body>
</html> 