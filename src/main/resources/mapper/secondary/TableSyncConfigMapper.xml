<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.secondary.TableSyncConfigMapper">
    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.example.demo.entity.secondary.TableSyncConfig">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="source_db" property="sourceDb" jdbcType="VARCHAR"/>
        <result column="source_table" property="sourceTable" jdbcType="VARCHAR"/>
        <result column="target_table" property="targetTable" jdbcType="VARCHAR"/>
        <result column="sync_type" property="syncType" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 公共列 -->
    <sql id="Base_Column_List">
        id, source_db, source_table, target_table, sync_type, status, create_time, update_time
    </sql>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap" parameterType="java.lang.Long">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM table_sync_config
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 查询所有有效配置 -->
    <select id="selectAllValid" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM table_sync_config
        WHERE status = 1
    </select>

    <!-- 根据源数据库和状态查询 -->
    <select id="selectBySourceDbAndStatus" resultMap="BaseResultMap">
        SELECT 
        <include refid="Base_Column_List"/>
        FROM table_sync_config
        WHERE source_db = #{sourceDb,jdbcType=VARCHAR}
        AND status = #{status,jdbcType=TINYINT}
    </select>

    <!-- 插入 -->
    <insert id="insert" parameterType="com.example.demo.entity.secondary.TableSyncConfig" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO table_sync_config (
            source_db, 
            source_table, 
            target_table, 
            sync_type, 
            status
        )
        VALUES (
            #{sourceDb,jdbcType=VARCHAR}, 
            #{sourceTable,jdbcType=VARCHAR}, 
            #{targetTable,jdbcType=VARCHAR}, 
            #{syncType,jdbcType=VARCHAR}, 
            #{status,jdbcType=TINYINT}
        )
    </insert>

    <!-- 更新 -->
    <update id="update" parameterType="com.example.demo.entity.secondary.TableSyncConfig">
        UPDATE table_sync_config
        <set>
            <if test="sourceDb != null">
                source_db = #{sourceDb,jdbcType=VARCHAR},
            </if>
            <if test="sourceTable != null">
                source_table = #{sourceTable,jdbcType=VARCHAR},
            </if>
            <if test="targetTable != null">
                target_table = #{targetTable,jdbcType=VARCHAR},
            </if>
            <if test="syncType != null">
                sync_type = #{syncType,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
        </set>
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM table_sync_config
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>
</mapper> 