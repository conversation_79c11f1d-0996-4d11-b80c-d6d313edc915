<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.secondary.ProdBugMapper">
    
    <select id="findByDateRange" resultType="com.example.demo.entity.secondary.ProdBug">
        SELECT 
            ID as id,
            标题,
            缺陷分类,
            处理人,
            开发人员,
            测试人员,
            创建人,
            创建时间,
            修复人,
            发现日期,
            缺陷引入日期,
            月份,
            一级分类,
            二级分类
        FROM prod_bugs
        WHERE 1=1
        <if test="startMonth != null and startMonth != ''">
            AND 月份 >= #{startMonth}
        </if>
        <if test="endMonth != null and endMonth != ''">
            AND 月份 &lt;= #{endMonth}
        </if>
        ORDER BY 月份, 一级分类
    </select>

    <select id="findDistinctCategories" resultType="string">
        SELECT DISTINCT 一级分类
        FROM prod_bugs
        WHERE 一级分类 IS NOT NULL
        ORDER BY 一级分类
    </select>
</mapper> 