<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.DevCommitsWeeklyMapper">
    
    <select id="findByConditions" resultType="com.example.demo.entity.primary.DevCommitsWeekly">
        SELECT d.username, d.weekly, d.total_lines as totalLines
        FROM dev_commits_weekly d
        LEFT JOIN base_dever_info b ON d.username = b.研发人员
        WHERE 1=1
        <if test="team != null and team != ''">
            AND b.团队 = #{team}
        </if>
        <if test="usernames != null and usernames.size() > 0">
            AND d.username IN
            <foreach item="username" collection="usernames" open="(" separator="," close=")">
                #{username}
            </foreach>
        </if>
        <if test="startWeek != null and startWeek != ''">
            AND d.weekly >= #{startWeek}
        </if>
        <if test="endWeek != null and endWeek != ''">
            AND d.weekly &lt;= #{endWeek}
        </if>
        ORDER BY d.weekly DESC, d.username
    </select>

    <select id="findDistinctWeeks" resultType="string">
        SELECT DISTINCT weekly
        FROM dev_commits_weekly
        ORDER BY weekly DESC
    </select>

    <select id="findUsernamesByTeam" resultType="string">
        SELECT DISTINCT d.username
        FROM dev_commits_weekly d
        INNER JOIN base_dever_info b ON d.username = b.研发人员
        WHERE b.团队 = #{team}
        ORDER BY d.username
    </select>

    <select id="findDistinctTeams" resultType="string">
        SELECT DISTINCT 团队
        FROM base_dever_info
        ORDER BY 团队
    </select>
</mapper> 