<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
代码提交统计Mapper映射文件
<AUTHOR>
@date 2025-08-20 16:01:37
-->
<mapper namespace="com.example.demo.mapper.primary.CommitStatsMapper">
    
    <!-- 年度统计查询 -->
    <select id="selectYearlyStats" resultType="com.example.demo.dto.CommitStatsResponseDTO">
        SELECT 
            YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) as period,
            SUM(dcm.total_lines) as totalLines,
            COUNT(DISTINCT dcm.username) as developerCount,
            ROUND(SUM(dcm.total_lines) / COUNT(DISTINCT dcm.username)) as avgLinesPerDeveloper
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
        </where>
        GROUP BY YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m'))
        ORDER BY period
    </select>
    
    <!-- 季度统计查询 -->
    <select id="selectQuarterlyStats" resultType="com.example.demo.dto.CommitStatsResponseDTO">
        SELECT 
            CONCAT(YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), '-Q', QUARTER(STR_TO_DATE(dcm.monthly, '%Y-%m'))) as period,
            SUM(dcm.total_lines) as totalLines,
            COUNT(DISTINCT dcm.username) as developerCount,
            ROUND(SUM(dcm.total_lines) / COUNT(DISTINCT dcm.username)) as avgLinesPerDeveloper
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
        </where>
        GROUP BY YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), QUARTER(STR_TO_DATE(dcm.monthly, '%Y-%m'))
        ORDER BY period
    </select>
    
    <!-- 半年度统计查询 -->
    <select id="selectHalfYearlyStats" resultType="com.example.demo.dto.CommitStatsResponseDTO">
        SELECT 
            CONCAT(YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), '-H', IF(MONTH(STR_TO_DATE(dcm.monthly, '%Y-%m')) &lt;= 6, '1', '2')) as period,
            SUM(dcm.total_lines) as totalLines,
            COUNT(DISTINCT dcm.username) as developerCount,
            ROUND(SUM(dcm.total_lines) / COUNT(DISTINCT dcm.username)) as avgLinesPerDeveloper
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
        </where>
        GROUP BY YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), IF(MONTH(STR_TO_DATE(dcm.monthly, '%Y-%m')) &lt;= 6, '1', '2')
        ORDER BY period
    </select>
    
    <!-- 团队年度统计查询 -->
    <select id="selectTeamYearlyStats" resultType="com.example.demo.dto.CommitStatsResponseDTO">
        SELECT 
            YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) as period,
            COALESCE(all_dever.团队, '未分配团队') as team,
            SUM(dcm.total_lines) as totalLines,
            COUNT(DISTINCT dcm.username) as developerCount,
            ROUND(SUM(dcm.total_lines) / COUNT(DISTINCT dcm.username)) as avgLinesPerDeveloper
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
        </where>
        GROUP BY YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), all_dever.团队
        ORDER BY period, team
    </select>
    
    <!-- 团队半年度统计查询 -->
    <select id="selectTeamHalfYearlyStats" resultType="com.example.demo.dto.CommitStatsResponseDTO">
        SELECT 
            CONCAT(YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), '-H', IF(MONTH(STR_TO_DATE(dcm.monthly, '%Y-%m')) &lt;= 6, '1', '2')) as period,
            COALESCE(all_dever.团队, '未分配团队') as team,
            SUM(dcm.total_lines) as totalLines,
            COUNT(DISTINCT dcm.username) as developerCount,
            ROUND(SUM(dcm.total_lines) / COUNT(DISTINCT dcm.username)) as avgLinesPerDeveloper
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
        </where>
        GROUP BY YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), IF(MONTH(STR_TO_DATE(dcm.monthly, '%Y-%m')) &lt;= 6, '1', '2'), all_dever.团队
        ORDER BY period, team
    </select>
    
    <!-- 团队季度统计查询 -->
    <select id="selectTeamQuarterlyStats" resultType="com.example.demo.dto.CommitStatsResponseDTO">
        SELECT 
            CONCAT(YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), '-Q', QUARTER(STR_TO_DATE(dcm.monthly, '%Y-%m'))) as period,
            COALESCE(all_dever.团队, '未分配团队') as team,
            SUM(dcm.total_lines) as totalLines,
            COUNT(DISTINCT dcm.username) as developerCount,
            ROUND(SUM(dcm.total_lines) / COUNT(DISTINCT dcm.username)) as avgLinesPerDeveloper
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
        </where>
        GROUP BY YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), QUARTER(STR_TO_DATE(dcm.monthly, '%Y-%m')), all_dever.团队
        ORDER BY period, team
    </select>
    
    <!-- 统计汇总数据查询 -->
    <select id="selectStatsSummary" resultType="com.example.demo.dto.CommitStatsSummaryDTO">
        SELECT 
            '汇总' as period,
            SUM(dcm.total_lines) as totalLines,
            COUNT(DISTINCT dcm.username) as developerCount,
            COUNT(DISTINCT all_dever.团队) as teamCount,
            ROUND(SUM(dcm.total_lines) / COUNT(DISTINCT dcm.username), 2) as avgLinesPerDeveloper
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
        </where>
    </select>
    
    <!-- 查询所有可用年份 -->
    <select id="selectAvailableYears" resultType="java.lang.Integer">
        SELECT DISTINCT YEAR(STR_TO_DATE(monthly, '%Y-%m')) as year 
        FROM dev_commits_monthly 
        ORDER BY year DESC
    </select>
    
    <!-- 查询所有团队信息 -->
    <select id="selectAllTeams" resultType="java.lang.String">
        SELECT DISTINCT 团队 
        FROM (
            SELECT 团队 FROM base_dever_info WHERE 团队 IS NOT NULL
            UNION
            SELECT 团队 FROM base_dever_info_leave WHERE 团队 IS NOT NULL
        ) all_teams
        ORDER BY 团队
    </select>
    
</mapper>