<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.DevCommitsMonthlyMapper">
    
    <select id="findAll" resultType="com.example.demo.entity.primary.DevCommitsMonthly">
        SELECT d.username, d.monthly, d.total_lines as totalLines 
        FROM dev_commits_monthly d
        ORDER BY d.monthly
    </select>

    <select id="findByUsernames" resultType="com.example.demo.entity.primary.DevCommitsMonthly">
        SELECT d.username, d.monthly, d.total_lines as totalLines 
        FROM dev_commits_monthly d
        WHERE d.username IN 
        <foreach item="username" collection="list" open="(" separator="," close=")">
            #{username}
        </foreach>
        ORDER BY d.monthly
    </select>

    <select id="findByTeamAndDateRange" resultType="com.example.demo.entity.primary.DevCommitsMonthly">
        SELECT d.username, d.monthly, d.total_lines as totalLines 
        FROM dev_commits_monthly d
        INNER JOIN base_dever_info b ON d.username = b.研发人员
        WHERE b.研发人员 is not null
        <if test="team != null and team != ''">
            AND b.团队 = #{team}
        </if>
        <if test="startMonth != null and startMonth != ''">
            AND d.monthly >= #{startMonth}
        </if>
        <if test="endMonth != null and endMonth != ''">
            AND d.monthly &lt;= #{endMonth}
        </if>
        ORDER BY d.monthly
    </select>

    <select id="findDistinctUsernames" resultType="string">
        SELECT DISTINCT d.username 
        FROM dev_commits_monthly d
        ORDER BY d.username
    </select>

    <select id="findDistinctMonthly" resultType="string">
        SELECT DISTINCT monthly 
        FROM dev_commits_monthly 
        ORDER BY monthly
    </select>

    <select id="findDistinctTeams" resultType="string">
        SELECT DISTINCT 团队 as team
        FROM base_dever_info 
        ORDER BY team
    </select>

    <select id="findUsernamesByTeam" resultType="string">
        SELECT DISTINCT d.username 
        FROM dev_commits_monthly d
        INNER JOIN base_dever_info b ON d.username = b.研发人员
        WHERE b.团队 = #{team}
        ORDER BY d.username
    </select>
</mapper> 