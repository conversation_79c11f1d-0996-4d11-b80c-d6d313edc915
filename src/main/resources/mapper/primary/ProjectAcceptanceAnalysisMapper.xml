<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.ProjectAcceptanceAnalysisMapper">

    <!-- 获取所有项目名列表（去重） -->
    <select id="findDistinctProjectNames" resultType="java.lang.String">
        SELECT DISTINCT project_name
        FROM gray_data
        WHERE project_name IS NOT NULL AND project_name != ''
        ORDER BY project_name
    </select>

    <!-- 根据上线日期获取最新的项目名 -->
    <select id="findLatestProjectByOnlineDate" resultType="java.lang.String">
        SELECT project_name
        FROM gray_data
        WHERE online_date IS NOT NULL AND online_date != ''
        ORDER BY STR_TO_DATE(online_date, '%Y-%m-%d') DESC
        LIMIT 1
    </select>

    <!-- 获取开发问题分类统计数据 -->
    <select id="getDevIssueCategoryStats" resultType="java.util.Map">
        SELECT
            dev_issue_category as category,
            COUNT(*) as count
        FROM gray_data
        WHERE 1=1
        <if test="projectName != null and projectName != ''">
            AND project_name = #{projectName}
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        AND dev_issue_category IS NOT NULL AND dev_issue_category != ''
        GROUP BY dev_issue_category
        ORDER BY
            CASE dev_issue_category
                WHEN '需求理解错误' THEN 1
                WHEN '代码逻辑错误' THEN 2
                WHEN '边界条件处理' THEN 3
                WHEN '异常处理不当' THEN 4
                WHEN '性能问题' THEN 5
                WHEN '接口对接问题' THEN 6
                WHEN '数据库操作问题' THEN 7
                WHEN '配置问题' THEN 8
                WHEN '第三方依赖问题' THEN 9
                WHEN '其他' THEN 10
                ELSE 11
            END,
            dev_issue_category
    </select>

    <!-- 获取测试问题分类统计数据 -->
    <select id="getTestIssueCategoryStats" resultType="java.util.Map">
        SELECT
            test_issue_category as category,
            COUNT(*) as count
        FROM gray_data
        WHERE 1=1
        <if test="projectName != null and projectName != ''">
            AND project_name = #{projectName}
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        AND test_issue_category IS NOT NULL AND test_issue_category != ''
        GROUP BY test_issue_category
        ORDER BY
            CASE test_issue_category
                WHEN '用例设计不充分' THEN 1
                WHEN '测试环境问题' THEN 2
                WHEN '测试数据问题' THEN 3
                WHEN '测试工具问题' THEN 4
                WHEN '测试流程问题' THEN 5
                WHEN '回归测试遗漏' THEN 6
                WHEN '兼容性测试不足' THEN 7
                WHEN '性能测试不足' THEN 8
                WHEN '安全测试不足' THEN 9
                WHEN '其他' THEN 10
                ELSE 11
            END,
            test_issue_category
    </select>

    <!-- 获取测试人员Bug数统计数据 -->
    <select id="getTesterBugStats" resultType="java.util.Map">
        SELECT 
            tester,
            COUNT(*) as count
        FROM gray_data
        WHERE 1=1
        <if test="projectName != null and projectName != ''">
            AND project_name = #{projectName}
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        AND tester IS NOT NULL AND tester != ''
        GROUP BY tester
        ORDER BY count DESC
    </select>

    <!-- 获取开发人员Bug数统计数据 -->
    <select id="getDeveloperBugStats" resultType="java.util.Map">
        SELECT 
            dev_contact as developer,
            COUNT(*) as count
        FROM gray_data
        WHERE 1=1
        <if test="projectName != null and projectName != ''">
            AND project_name = #{projectName}
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        AND dev_contact IS NOT NULL AND dev_contact != ''
        GROUP BY dev_contact
        ORDER BY count DESC
    </select>

    <!-- 获取开发人员问题分类详细统计数据 -->
    <select id="getDeveloperIssueDetailStats" resultType="java.util.Map">
        SELECT 
            dev_contact as developer,
            dev_issue_category as category,
            COUNT(*) as count
        FROM gray_data
        WHERE 1=1
        <if test="projectName != null and projectName != ''">
            AND project_name = #{projectName}
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        AND dev_contact IS NOT NULL AND dev_contact != ''
        AND dev_issue_category IS NOT NULL AND dev_issue_category != ''
        GROUP BY dev_contact, dev_issue_category
        ORDER BY dev_contact, count DESC
    </select>

    <!-- 获取测试人员问题分类详细统计数据 -->
    <select id="getTesterIssueDetailStats" resultType="java.util.Map">
        SELECT 
            tester,
            test_issue_category as category,
            COUNT(*) as count
        FROM gray_data
        WHERE 1=1
        <if test="projectName != null and projectName != ''">
            AND project_name = #{projectName}
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        AND tester IS NOT NULL AND tester != ''
        AND test_issue_category IS NOT NULL AND test_issue_category != ''
        GROUP BY tester, test_issue_category
        ORDER BY tester, count DESC
    </select>

</mapper>
