<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.ProjectAnalysisMapper">

    <!-- 结果映射 -->
    <resultMap id="ProjectAnalysisResultMap" type="com.example.demo.entity.primary.ProjectAnalysis">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="project_name" property="projectName" jdbcType="VARCHAR"/>
        <result column="analysis_content" property="analysisContent" jdbcType="LONGVARCHAR"/>
        <result column="online_date" property="onlineDate" jdbcType="VARCHAR"/>
        <result column="gray_date" property="grayDate" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 根据项目名称查询项目分析报告 -->
    <select id="findByProjectName" resultMap="ProjectAnalysisResultMap">
        SELECT id, project_name, analysis_content, online_date, gray_date, create_time, update_time
        FROM project_analysis
        WHERE project_name = #{projectName}
        LIMIT 1
    </select>

    <!-- 插入项目分析报告 -->
    <insert id="insert" parameterType="com.example.demo.entity.primary.ProjectAnalysis" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO project_analysis (project_name, analysis_content, online_date, gray_date, create_time, update_time)
        VALUES (#{projectName}, #{analysisContent}, #{onlineDate}, #{grayDate}, NOW(), NOW())
    </insert>

    <!-- 更新项目分析报告 -->
    <update id="update" parameterType="com.example.demo.entity.primary.ProjectAnalysis">
        UPDATE project_analysis
        SET analysis_content = #{analysisContent},
            online_date = #{onlineDate},
            gray_date = #{grayDate},
            update_time = NOW()
        WHERE project_name = #{projectName}
    </update>

    <!-- 根据项目名称删除项目分析报告 -->
    <delete id="deleteByProjectName">
        DELETE FROM project_analysis WHERE project_name = #{projectName}
    </delete>

</mapper>
