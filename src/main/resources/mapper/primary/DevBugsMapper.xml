<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.DevBugsMapper">
    
    <select id="findByConditions" resultType="com.example.demo.entity.primary.DevBugsOfCommits">
        SELECT d.mode, d.untilDay, d.fixer, d.bugs, d.bug_total as bugTotal, d.code_total as codeTotal
        FROM dev_bugs_of_commits_monthly d
        LEFT JOIN base_dever_info b ON d.fixer = b.研发人员
        WHERE 1=1
        <if test="mode != null and mode != ''">
            AND d.mode = #{mode}
        </if>
        <if test="team != null and team != ''">
            AND b.团队 = #{team}
        </if>
        <if test="usernames != null and usernames.size() > 0">
            AND d.fixer IN
            <foreach item="username" collection="usernames" open="(" separator="," close=")">
                #{username}
            </foreach>
        </if>
        <if test="startMonth != null and startMonth != ''">
            AND d.untilDay >= #{startMonth}
        </if>
        <if test="endMonth != null and endMonth != ''">
            AND d.untilDay &lt;= #{endMonth}
        </if>
        ORDER BY d.untilDay, d.fixer
    </select>

    <select id="findDistinctMonths" resultType="string">
        SELECT DISTINCT untilDay
        FROM dev_bugs_of_commits_monthly
        ORDER BY untilDay
    </select>

    <select id="findUsernamesByTeam" resultType="string">
        SELECT DISTINCT d.fixer
        FROM dev_bugs_of_commits_monthly d
        INNER JOIN base_dever_info b ON d.fixer = b.研发人员
        WHERE b.团队 = #{team}
        ORDER BY d.fixer
    </select>
</mapper> 