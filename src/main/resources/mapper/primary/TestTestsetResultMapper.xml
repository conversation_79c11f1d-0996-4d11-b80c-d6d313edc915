<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.TestTestsetResultMapper">
  <resultMap id="BaseResultMap" type="com.example.demo.entity.primary.TestTestsetResult">
    <!--@mbg.generated-->
    <!--@Table test_testset_result-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="执行时间" jdbcType="TIMESTAMP" property="执行时间" />
    <result column="运行批id" jdbcType="VARCHAR" property="运行批id" />
    <result column="测试集id" jdbcType="INTEGER" property="测试集id" />
    <result column="测试集名称" jdbcType="VARCHAR" property="测试集名称" />
    <result column="应用名" jdbcType="VARCHAR" property="应用名" />
    <result column="测试案例版本" jdbcType="VARCHAR" property="测试案例版本" />
    <result column="执行环境" jdbcType="VARCHAR" property="执行环境" />
    <result column="接口通过率" jdbcType="VARCHAR" property="接口通过率" />
    <result column="案例通过率" jdbcType="VARCHAR" property="案例通过率" />
    <result column="耗时" jdbcType="DOUBLE" property="耗时" />
    <result column="是否偏差度小" jdbcType="TINYINT" property="是否偏差度小" />
    <result column="create_date" jdbcType="TIMESTAMP" property="createDate" />
  </resultMap>
  
  <resultMap id="TestExecutionStatsMap" type="com.example.demo.entity.primary.TestExecutionStats">
    <result column="executeDate" jdbcType="VARCHAR" property="executeDate" />
    <result column="appName" jdbcType="VARCHAR" property="appName" />
    <result column="totalDuration" jdbcType="DOUBLE" property="totalDuration" />
    <result column="avgPassRate" jdbcType="VARCHAR" property="avgPassRate" />
    <result column="startTime" jdbcType="TIMESTAMP" property="startTime" />
    <result column="endTime" jdbcType="TIMESTAMP" property="endTime" />
    <result column="executeEnv" jdbcType="VARCHAR" property="executeEnv" />
  </resultMap>
  
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, 执行时间, 运行批id, 测试集id, 测试集名称, 应用名, 测试案例版本, 执行环境, 接口通过率, 案例通过率, 耗时, 是否偏差度小, create_date
  </sql>
  
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select 
    <include refid="Base_Column_List" />
    from test_testset_result
    where id = #{id,jdbcType=BIGINT}
  </select>
  
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from test_testset_result
    where id = #{id,jdbcType=BIGINT}
  </delete>
  
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.example.demo.entity.primary.TestTestsetResult" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into test_testset_result (执行时间, 运行批id, 测试集id, 
      测试集名称, 应用名, 测试案例版本, 
      执行环境, 接口通过率, 案例通过率, 
      耗时, 是否偏差度小, create_date
      )
    values (#{执行时间,jdbcType=TIMESTAMP}, #{运行批id,jdbcType=VARCHAR}, #{测试集id,jdbcType=INTEGER}, 
      #{测试集名称,jdbcType=VARCHAR}, #{应用名,jdbcType=VARCHAR}, #{测试案例版本,jdbcType=VARCHAR}, 
      #{执行环境,jdbcType=VARCHAR}, #{接口通过率,jdbcType=VARCHAR}, #{案例通过率,jdbcType=VARCHAR}, 
      #{耗时,jdbcType=DOUBLE}, #{是否偏差度小,jdbcType=TINYINT}, #{createDate,jdbcType=TIMESTAMP}
      )
  </insert>
  
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.example.demo.entity.primary.TestTestsetResult" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into test_testset_result
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="执行时间 != null">
        执行时间,
      </if>
      <if test="运行批id != null">
        运行批id,
      </if>
      <if test="测试集id != null">
        测试集id,
      </if>
      <if test="测试集名称 != null">
        测试集名称,
      </if>
      <if test="应用名 != null">
        应用名,
      </if>
      <if test="测试案例版本 != null">
        测试案例版本,
      </if>
      <if test="执行环境 != null">
        执行环境,
      </if>
      <if test="接口通过率 != null">
        接口通过率,
      </if>
      <if test="案例通过率 != null">
        案例通过率,
      </if>
      <if test="耗时 != null">
        耗时,
      </if>
      <if test="是否偏差度小 != null">
        是否偏差度小,
      </if>
      <if test="createDate != null">
        create_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="执行时间 != null">
        #{执行时间,jdbcType=TIMESTAMP},
      </if>
      <if test="运行批id != null">
        #{运行批id,jdbcType=VARCHAR},
      </if>
      <if test="测试集id != null">
        #{测试集id,jdbcType=INTEGER},
      </if>
      <if test="测试集名称 != null">
        #{测试集名称,jdbcType=VARCHAR},
      </if>
      <if test="应用名 != null">
        #{应用名,jdbcType=VARCHAR},
      </if>
      <if test="测试案例版本 != null">
        #{测试案例版本,jdbcType=VARCHAR},
      </if>
      <if test="执行环境 != null">
        #{执行环境,jdbcType=VARCHAR},
      </if>
      <if test="接口通过率 != null">
        #{接口通过率,jdbcType=VARCHAR},
      </if>
      <if test="案例通过率 != null">
        #{案例通过率,jdbcType=VARCHAR},
      </if>
      <if test="耗时 != null">
        #{耗时,jdbcType=DOUBLE},
      </if>
      <if test="是否偏差度小 != null">
        #{是否偏差度小,jdbcType=TINYINT},
      </if>
      <if test="createDate != null">
        #{createDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  
  <update id="updateByPrimaryKeySelective" parameterType="com.example.demo.entity.primary.TestTestsetResult">
    <!--@mbg.generated-->
    update test_testset_result
    <set>
      <if test="执行时间 != null">
        执行时间 = #{执行时间,jdbcType=TIMESTAMP},
      </if>
      <if test="运行批id != null">
        运行批id = #{运行批id,jdbcType=VARCHAR},
      </if>
      <if test="测试集id != null">
        测试集id = #{测试集id,jdbcType=INTEGER},
      </if>
      <if test="测试集名称 != null">
        测试集名称 = #{测试集名称,jdbcType=VARCHAR},
      </if>
      <if test="应用名 != null">
        应用名 = #{应用名,jdbcType=VARCHAR},
      </if>
      <if test="测试案例版本 != null">
        测试案例版本 = #{测试案例版本,jdbcType=VARCHAR},
      </if>
      <if test="执行环境 != null">
        执行环境 = #{执行环境,jdbcType=VARCHAR},
      </if>
      <if test="接口通过率 != null">
        接口通过率 = #{接口通过率,jdbcType=VARCHAR},
      </if>
      <if test="案例通过率 != null">
        案例通过率 = #{案例通过率,jdbcType=VARCHAR},
      </if>
      <if test="耗时 != null">
        耗时 = #{耗时,jdbcType=DOUBLE},
      </if>
      <if test="是否偏差度小 != null">
        是否偏差度小 = #{是否偏差度小,jdbcType=TINYINT},
      </if>
      <if test="createDate != null">
        create_date = #{createDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <update id="updateByPrimaryKey" parameterType="com.example.demo.entity.primary.TestTestsetResult">
    <!--@mbg.generated-->
    update test_testset_result
    set 执行时间 = #{执行时间,jdbcType=TIMESTAMP},
      运行批id = #{运行批id,jdbcType=VARCHAR},
      测试集id = #{测试集id,jdbcType=INTEGER},
      测试集名称 = #{测试集名称,jdbcType=VARCHAR},
      应用名 = #{应用名,jdbcType=VARCHAR},
      测试案例版本 = #{测试案例版本,jdbcType=VARCHAR},
      执行环境 = #{执行环境,jdbcType=VARCHAR},
      接口通过率 = #{接口通过率,jdbcType=VARCHAR},
      案例通过率 = #{案例通过率,jdbcType=VARCHAR},
      耗时 = #{耗时,jdbcType=DOUBLE},
      是否偏差度小 = #{是否偏差度小,jdbcType=TINYINT},
      create_date = #{createDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  
  <!-- 查询自动化测试执行统计数据 -->
  <select id="queryTestExecutionStats" resultMap="TestExecutionStatsMap">
    SELECT 
      DATE_FORMAT(执行时间, '%Y%m%d') as executeDate,
      应用名 as appName,
      SUM(耗时) as totalDuration,
      CONCAT(ROUND(AVG(CAST(REPLACE(案例通过率, '%', '') AS DECIMAL(10,2))), 2), '%') as avgPassRate,
      MIN(执行时间) as startTime,
      MAX(执行时间) as endTime,
      执行环境 as executeEnv
    FROM 
      test_testset_result
    WHERE 
      1=1
      <if test="startDate != null and startDate != ''">
        AND DATE_FORMAT(执行时间, '%Y%m%d') &gt;= #{startDate}
      </if>
      <if test="endDate != null and endDate != ''">
        AND DATE_FORMAT(执行时间, '%Y%m%d') &lt;= #{endDate}
      </if>
      <if test="appName != null and appName != ''">
        AND 应用名 = #{appName}
      </if>
      <if test="executeEnv != null and executeEnv != ''">
        AND 执行环境 = #{executeEnv}
      </if>
    GROUP BY 
      DATE_FORMAT(执行时间, '%Y%m%d'), 应用名, 执行环境
    ORDER BY 
      executeDate ASC, appName ASC
  </select>
  
  <!-- 获取所有应用名列表（去重） -->
  <select id="findDistinctAppNames" resultType="java.lang.String">
    SELECT DISTINCT 应用名
    FROM test_testset_result
    WHERE 应用名 IS NOT NULL AND 应用名 != ''
    ORDER BY 应用名 ASC
  </select>
  
  <!-- 获取所有执行环境列表（去重） -->
  <select id="findDistinctExecuteEnvs" resultType="java.lang.String">
    SELECT DISTINCT 执行环境
    FROM test_testset_result
    WHERE 执行环境 IS NOT NULL AND 执行环境 != ''
    ORDER BY 执行环境 ASC
  </select>
</mapper>