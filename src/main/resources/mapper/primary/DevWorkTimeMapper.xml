<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.DevWorkTimeMapper">
    
    <select id="findByConditions" resultType="com.example.demo.entity.primary.DevWorkTimeSpent">
        SELECT id, duration, owner, 任务类型 as taskType, effort, team_name as teamName
        FROM dev_worktime_spent_segregated
        WHERE 1=1
        AND owner != '谢宏栋'
        <if test="team != null and team != ''">
            AND team_name = #{team}
        </if>
        <if test="usernames != null and usernames.size() > 0">
            AND owner IN
            <foreach item="username" collection="usernames" open="(" separator="," close=")">
                #{username}
            </foreach>
        </if>
        <if test="startMonth != null and startMonth != ''">
            AND duration >= #{startMonth}
        </if>
        <if test="endMonth != null and endMonth != ''">
            AND duration &lt;= #{endMonth}
        </if>
        ORDER BY duration, owner
    </select>

    <select id="findDistinctUsers" resultType="string">
        SELECT DISTINCT owner
        FROM dev_worktime_spent_segregated
        WHERE 1=1
        AND owner != '谢宏栋'
        <if test="team != null and team != ''">
            AND team_name = #{team}
        </if>
        ORDER BY owner
    </select>

    <select id="findDistinctTeams" resultType="string">
        SELECT DISTINCT team_name
        FROM dev_worktime_spent_segregated
        WHERE owner != '谢宏栋'
        ORDER BY team_name
    </select>
</mapper> 