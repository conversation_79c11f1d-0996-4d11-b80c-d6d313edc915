<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<!--
个人代码提交统计Mapper映射文件
<AUTHOR>
@date 2025-08-23 10:15:55
-->
<mapper namespace="com.example.demo.mapper.primary.PersonalCommitStatsMapper">
    
    <!-- 个人年度统计查询 -->
    <select id="selectPersonalYearlyStats" resultType="com.example.demo.dto.PersonalCommitStatsResponseDTO">
        SELECT 
            YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) as period,
            dcm.username,
            COALESCE(all_dever.团队, '未分配团队') as team,
            SUM(dcm.total_lines) as totalLines,
            ROUND(SUM(dcm.total_lines) / 
                CASE 
                    WHEN YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) = YEAR(NOW()) 
                    THEN GREATEST(1, MONTH(NOW()) - 1)
                    ELSE 12 
                END
            ) as avgMonthlyLines
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
            <if test="request.usernames != null and request.usernames.size() > 0">
                AND dcm.username IN 
                <foreach collection="request.usernames" item="username" open="(" close=")" separator=",">
                    #{username}
                </foreach>
            </if>
        </where>
        GROUP BY YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), dcm.username, all_dever.团队
        ORDER BY period, totalLines DESC
    </select>
    
    <!-- 个人季度统计查询 -->
    <select id="selectPersonalQuarterlyStats" resultType="com.example.demo.dto.PersonalCommitStatsResponseDTO">
        SELECT 
            CONCAT(YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), '-Q', QUARTER(STR_TO_DATE(dcm.monthly, '%Y-%m'))) as period,
            dcm.username,
            COALESCE(all_dever.团队, '未分配团队') as team,
            SUM(dcm.total_lines) as totalLines,
            ROUND(SUM(dcm.total_lines) / 
                CASE 
                    WHEN YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) = YEAR(NOW()) AND QUARTER(STR_TO_DATE(dcm.monthly, '%Y-%m')) = QUARTER(NOW())
                    THEN GREATEST(1, ((MONTH(NOW()) - 1) % 3) + 1 - 1)
                    ELSE 3 
                END
            ) as avgMonthlyLines
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
            <if test="request.usernames != null and request.usernames.size() > 0">
                AND dcm.username IN 
                <foreach collection="request.usernames" item="username" open="(" close=")" separator=",">
                    #{username}
                </foreach>
            </if>
        </where>
        GROUP BY YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), QUARTER(STR_TO_DATE(dcm.monthly, '%Y-%m')), dcm.username, all_dever.团队
        ORDER BY period, totalLines DESC
    </select>
    
    <!-- 个人半年度统计查询 -->
    <select id="selectPersonalHalfYearlyStats" resultType="com.example.demo.dto.PersonalCommitStatsResponseDTO">
        SELECT 
            CONCAT(YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), '-H', IF(MONTH(STR_TO_DATE(dcm.monthly, '%Y-%m')) &lt;= 6, '1', '2')) as period,
            dcm.username,
            COALESCE(all_dever.团队, '未分配团队') as team,
            SUM(dcm.total_lines) as totalLines,
            ROUND(SUM(dcm.total_lines) / 
                CASE 
                    WHEN YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) = YEAR(NOW()) 
                         AND IF(MONTH(STR_TO_DATE(dcm.monthly, '%Y-%m')) &lt;= 6, '1', '2') = IF(MONTH(NOW()) &lt;= 6, '1', '2')
                    THEN GREATEST(1, 
                         IF(MONTH(NOW()) &lt;= 6, 
                            MONTH(NOW()) - 1, 
                            MONTH(NOW()) - 7)
                         )
                    ELSE 6 
                END
            ) as avgMonthlyLines
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
            <if test="request.usernames != null and request.usernames.size() > 0">
                AND dcm.username IN 
                <foreach collection="request.usernames" item="username" open="(" close=")" separator=",">
                    #{username}
                </foreach>
            </if>
        </where>
        GROUP BY YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')), IF(MONTH(STR_TO_DATE(dcm.monthly, '%Y-%m')) &lt;= 6, '1', '2'), dcm.username, all_dever.团队
        ORDER BY period, totalLines DESC
    </select>
    
    <!-- Top N开发者排名查询 -->
    <select id="selectTopDevelopersRanking" resultType="com.example.demo.dto.PersonalCommitStatsResponseDTO">
        SELECT 
            '总排名' as period,
            dcm.username,
            COALESCE(all_dever.团队, '未分配团队') as team,
            SUM(dcm.total_lines) as totalLines,
            ROUND(AVG(dcm.total_lines)) as avgMonthlyLines
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        <where>
            <if test="request.years != null and request.years.size() > 0">
                AND YEAR(STR_TO_DATE(dcm.monthly, '%Y-%m')) IN 
                <foreach collection="request.years" item="year" open="(" close=")" separator=",">
                    #{year}
                </foreach>
            </if>
            <if test="request.teams != null and request.teams.size() > 0">
                AND all_dever.团队 IN 
                <foreach collection="request.teams" item="team" open="(" close=")" separator=",">
                    #{team}
                </foreach>
            </if>
        </where>
        GROUP BY dcm.username, all_dever.团队
        <choose>
            <when test="request.rankingType == 'AVG_LINES'">
                ORDER BY avgMonthlyLines DESC
            </when>
            <otherwise>
                ORDER BY totalLines DESC
            </otherwise>
        </choose>
        <if test="request.topLimit != null and request.topLimit > 0">
            LIMIT #{request.topLimit}
        </if>
    </select>
    
    <!-- 查询所有开发者用户名 -->
    <select id="selectAllDevelopers" resultType="java.lang.String">
        SELECT DISTINCT dcm.username 
        FROM dev_commits_monthly dcm
        WHERE dcm.username IS NOT NULL
        ORDER BY dcm.username
    </select>
    
    <!-- 查询指定团队的开发者用户名 -->
    <select id="selectDevelopersByTeams" resultType="java.lang.String">
        SELECT DISTINCT dcm.username
        FROM dev_commits_monthly dcm
        LEFT JOIN (
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info
            UNION
            SELECT 研发人员, 团队, 小组, 工作范畴 FROM base_dever_info_leave
        ) all_dever ON dcm.username = all_dever.研发人员
        WHERE all_dever.团队 IN 
        <foreach collection="teams" item="team" open="(" close=")" separator=",">
            #{team}
        </foreach>
        AND dcm.username IS NOT NULL
        ORDER BY dcm.username
    </select>
    
</mapper>