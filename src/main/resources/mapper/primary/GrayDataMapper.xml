<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.GrayDataMapper">
    
    <resultMap id="BaseResultMap" type="com.example.demo.entity.primary.GrayData">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="project_name" jdbcType="VARCHAR" property="projectName" />
        <result column="acceptance_env" jdbcType="VARCHAR" property="acceptanceEnv" />
        <result column="platform" jdbcType="VARCHAR" property="platform" />
        <result column="issue_type" jdbcType="VARCHAR" property="issueType" />
        <result column="module" jdbcType="VARCHAR" property="module" />
        <result column="page" jdbcType="VARCHAR" property="page" />
        <result column="issue_description" jdbcType="LONGVARCHAR" property="issueDescription" />
        <result column="modify_progress" jdbcType="VARCHAR" property="modifyProgress" />
        <result column="current_testable" jdbcType="VARCHAR" property="currentTestable" />
        <result column="acceptor" jdbcType="VARCHAR" property="acceptor" />
        <result column="issue_attribution" jdbcType="VARCHAR" property="issueAttribution" />
        <result column="dev_contact" jdbcType="VARCHAR" property="devContact" />
        <result column="dev_remark" jdbcType="LONGVARCHAR" property="devRemark" />
        <result column="dev_issue_category" jdbcType="VARCHAR" property="devIssueCategory" />
        <result column="has_test_case" jdbcType="VARCHAR" property="hasTestCase" />
        <result column="test_issue_category" jdbcType="VARCHAR" property="testIssueCategory" />
        <result column="test_reason_analysis" jdbcType="LONGVARCHAR" property="testReasonAnalysis" />
        <result column="tester" jdbcType="VARCHAR" property="tester" />
        <result column="gray_date" jdbcType="VARCHAR" property="grayDate" />
        <result column="online_date" jdbcType="VARCHAR" property="onlineDate" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, project_name, acceptance_env, platform, issue_type, module, page,
        issue_description, modify_progress, current_testable, acceptor, issue_attribution,
        dev_contact, dev_remark, dev_issue_category, has_test_case, test_issue_category,
        test_reason_analysis, tester, gray_date, online_date, create_time, update_time
    </sql>
    
    <insert id="insert" parameterType="com.example.demo.entity.primary.GrayData">
        INSERT INTO gray_data (
            project_name, acceptance_env, platform, issue_type, module, page,
            issue_description, modify_progress, current_testable, acceptor, issue_attribution,
            dev_contact, dev_remark, dev_issue_category, has_test_case, test_issue_category,
            test_reason_analysis, tester, gray_date, online_date, create_time, update_time
        ) VALUES (
            #{projectName}, #{acceptanceEnv}, #{platform}, #{issueType}, #{module}, #{page},
            #{issueDescription}, #{modifyProgress}, #{currentTestable}, #{acceptor}, #{issueAttribution},
            #{devContact}, #{devRemark}, #{devIssueCategory}, #{hasTestCase}, #{testIssueCategory},
            #{testReasonAnalysis}, #{tester}, #{grayDate}, #{onlineDate}, #{createTime}, #{updateTime}
        )
    </insert>
    
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO gray_data (
            project_name, acceptance_env, platform, issue_type, module, page,
            issue_description, modify_progress, current_testable, acceptor, issue_attribution,
            dev_contact, dev_remark, dev_issue_category, has_test_case, test_issue_category,
            test_reason_analysis, tester, gray_date, online_date, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.projectName}, #{item.acceptanceEnv}, #{item.platform}, #{item.issueType},
                #{item.module}, #{item.page}, #{item.issueDescription}, #{item.modifyProgress},
                #{item.currentTestable}, #{item.acceptor}, #{item.issueAttribution}, #{item.devContact},
                #{item.devRemark}, #{item.devIssueCategory}, #{item.hasTestCase}, #{item.testIssueCategory},
                #{item.testReasonAnalysis}, #{item.tester}, #{item.grayDate}, #{item.onlineDate}, #{item.createTime}, #{item.updateTime}
            )
        </foreach>
    </insert>
    
    <select id="findByConditions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM gray_data
        <where>
            <if test="projectName != null and projectName != ''">
                AND project_name LIKE CONCAT('%', #{projectName}, '%')
            </if>
            <if test="acceptanceEnv != null and acceptanceEnv != ''">
                AND acceptance_env = #{acceptanceEnv}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>
    
    <select id="countByProjectName" resultType="int">
        SELECT COUNT(1) FROM gray_data WHERE project_name = #{projectName}
    </select>
    
    <select id="findDistinctProjectNames" resultType="string">
        SELECT DISTINCT project_name FROM gray_data ORDER BY project_name
    </select>
    
    <select id="findDistinctAcceptanceEnvs" resultType="string">
        SELECT DISTINCT acceptance_env FROM gray_data 
        WHERE acceptance_env IS NOT NULL AND acceptance_env != ''
        ORDER BY acceptance_env
    </select>
    
    <delete id="deleteByProjectName">
        DELETE FROM gray_data WHERE project_name = #{projectName}
    </delete>
    
</mapper>
