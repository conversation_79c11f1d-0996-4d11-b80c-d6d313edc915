<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.DevWorkTimeSpentStatsMapper">
    <select id="getWorkTimeStats" resultType="com.example.demo.entity.primary.DevWorkTimeSpentStats">
        SELECT 
            团队,
            名字,
            SUM(交付时长) as 交付时长,
            SUM(填写总时长) as 填写总时长,
            SUM(理论时长) as 理论时长
        FROM dev_worktime_spent
        GROUP BY 团队, 名字
        ORDER BY 团队, 名字
    </select>

    <select id="getDistinctTeams" resultType="string">
        SELECT DISTINCT 团队 
        FROM dev_worktime_spent 
        ORDER BY 团队
    </select>
</mapper> 