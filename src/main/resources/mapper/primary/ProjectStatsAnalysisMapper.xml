<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.ProjectStatsAnalysisMapper">

    <!-- 获取所有项目名列表（去重） -->
    <select id="findDistinctProjectNames" resultType="java.lang.String">
        SELECT DISTINCT project_name
        FROM gray_data
        WHERE project_name IS NOT NULL AND project_name != ''
        ORDER BY project_name
    </select>

    <!-- 获取最近半年内上线的项目列表 -->
    <select id="findRecentProjectsByOnlineDate" resultType="java.lang.String">
        SELECT DISTINCT project_name
        FROM gray_data
        WHERE project_name IS NOT NULL AND project_name != ''
          AND online_date IS NOT NULL AND online_date != ''
          AND online_date >= DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 6 MONTH), '%Y%m%d')
        ORDER BY online_date DESC
        LIMIT 10
    </select>

    <!-- 获取项目基础统计数据 -->
    <select id="getProjectBasicStats" resultType="java.util.Map">
        SELECT 
            project_name,
            COUNT(*) as total_issues,
            COUNT(DISTINCT dev_contact) as dev_count,
            COUNT(DISTINCT tester) as tester_count,
            COUNT(DISTINCT acceptor) as acceptor_count,
            COUNT(DISTINCT platform) as platform_count,
            COUNT(DISTINCT module) as module_count
        FROM gray_data
        WHERE 1=1
        <if test="projectNames != null and projectNames.size() > 0">
            AND project_name IN
            <foreach collection="projectNames" item="projectName" open="(" separator="," close=")">
                #{projectName}
            </foreach>
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        GROUP BY project_name
        ORDER BY project_name
    </select>

    <!-- 获取项目开发问题分类统计数据 -->
    <select id="getProjectDevIssueStats" resultType="java.util.Map">
        SELECT
            project_name,
            dev_issue_category as category,
            COUNT(*) as count
        FROM gray_data
        WHERE dev_issue_category IS NOT NULL AND dev_issue_category != ''
        <if test="projectNames != null and projectNames.size() > 0">
            AND project_name IN
            <foreach collection="projectNames" item="projectName" open="(" separator="," close=")">
                #{projectName}
            </foreach>
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        GROUP BY project_name, dev_issue_category
        ORDER BY project_name,
            CASE dev_issue_category
                WHEN '需求理解错误' THEN 1
                WHEN '代码逻辑错误' THEN 2
                WHEN '边界条件处理' THEN 3
                WHEN '异常处理不当' THEN 4
                WHEN '性能问题' THEN 5
                WHEN '接口对接问题' THEN 6
                WHEN '数据库操作问题' THEN 7
                WHEN '配置问题' THEN 8
                WHEN '第三方依赖问题' THEN 9
                WHEN '其他' THEN 10
                ELSE 11
            END,
            dev_issue_category
    </select>

    <!-- 获取项目测试问题分类统计数据 -->
    <select id="getProjectTestIssueStats" resultType="java.util.Map">
        SELECT
            project_name,
            test_issue_category as category,
            COUNT(*) as count
        FROM gray_data
        WHERE test_issue_category IS NOT NULL AND test_issue_category != ''
        <if test="projectNames != null and projectNames.size() > 0">
            AND project_name IN
            <foreach collection="projectNames" item="projectName" open="(" separator="," close=")">
                #{projectName}
            </foreach>
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        GROUP BY project_name, test_issue_category
        ORDER BY project_name,
            CASE test_issue_category
                WHEN '用例设计不充分' THEN 1
                WHEN '测试环境问题' THEN 2
                WHEN '测试数据问题' THEN 3
                WHEN '测试工具问题' THEN 4
                WHEN '测试流程问题' THEN 5
                WHEN '回归测试遗漏' THEN 6
                WHEN '兼容性测试不足' THEN 7
                WHEN '性能测试不足' THEN 8
                WHEN '安全测试不足' THEN 9
                WHEN '其他' THEN 10
                ELSE 11
            END,
            test_issue_category
    </select>

    <!-- 获取项目开发人员Bug数统计数据 -->
    <select id="getProjectDeveloperBugStats" resultType="java.util.Map">
        SELECT 
            project_name,
            dev_contact as developer,
            COUNT(*) as count
        FROM gray_data
        WHERE dev_contact IS NOT NULL AND dev_contact != ''
        <if test="projectNames != null and projectNames.size() > 0">
            AND project_name IN
            <foreach collection="projectNames" item="projectName" open="(" separator="," close=")">
                #{projectName}
            </foreach>
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        GROUP BY project_name, dev_contact
        ORDER BY project_name, count DESC
    </select>

    <!-- 获取项目测试人员Bug数统计数据 -->
    <select id="getProjectTesterBugStats" resultType="java.util.Map">
        SELECT 
            project_name,
            tester,
            COUNT(*) as count
        FROM gray_data
        WHERE tester IS NOT NULL AND tester != ''
        <if test="projectNames != null and projectNames.size() > 0">
            AND project_name IN
            <foreach collection="projectNames" item="projectName" open="(" separator="," close=")">
                #{projectName}
            </foreach>
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        GROUP BY project_name, tester
        ORDER BY project_name, count DESC
    </select>

    <!-- 获取项目质量指标统计数据 -->
    <select id="getProjectQualityIndicators" resultType="java.util.Map">
        SELECT 
            project_name,
            COUNT(*) as total_issues,
            COUNT(CASE WHEN dev_issue_category IS NOT NULL AND dev_issue_category != '' THEN 1 END) as dev_issues,
            COUNT(CASE WHEN test_issue_category IS NOT NULL AND test_issue_category != '' THEN 1 END) as test_issues,
            COUNT(CASE WHEN has_test_case = '是' THEN 1 END) as has_test_case_count,
            COUNT(CASE WHEN current_testable = '是' THEN 1 END) as testable_count,
            COUNT(CASE WHEN modify_progress = '已完成' THEN 1 END) as completed_count,
            ROUND(COUNT(CASE WHEN has_test_case = '是' THEN 1 END) * 100.0 / COUNT(*), 2) as test_case_coverage,
            ROUND(COUNT(CASE WHEN current_testable = '是' THEN 1 END) * 100.0 / COUNT(*), 2) as testable_rate,
            ROUND(COUNT(CASE WHEN modify_progress = '已完成' THEN 1 END) * 100.0 / COUNT(*), 2) as completion_rate
        FROM gray_data
        WHERE 1=1
        <if test="projectNames != null and projectNames.size() > 0">
            AND project_name IN
            <foreach collection="projectNames" item="projectName" open="(" separator="," close=")">
                #{projectName}
            </foreach>
        </if>
        <if test="grayDateStart != null and grayDateStart != ''">
            AND gray_date >= #{grayDateStart}
        </if>
        <if test="grayDateEnd != null and grayDateEnd != ''">
            AND gray_date &lt;= #{grayDateEnd}
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        GROUP BY project_name
        ORDER BY project_name
    </select>

</mapper>
