<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.demo.mapper.primary.ProjectStatsDataMapper">

    <!-- 获取项目统计数据列表 -->
    <select id="getProjectStatsData" resultType="java.util.Map">
        SELECT
            project_name,
            COUNT(CASE WHEN modify_progress IS NULL OR modify_progress = '' OR (modify_progress != '无需修复' AND modify_progress != '下个版本') THEN 1 END) as total_issues,
            COUNT(CASE WHEN (modify_progress IS NULL OR modify_progress = '' OR (modify_progress != '无需修复' AND modify_progress != '下个版本'))
                       AND dev_contact IS NOT NULL AND dev_contact != '' AND dev_contact != 'None' THEN 1 END) as dev_issues,
            COUNT(CASE WHEN (modify_progress IS NULL OR modify_progress = '' OR (modify_progress != '无需修复' AND modify_progress != '下个版本'))
                       AND tester IS NOT NULL AND tester != '' AND tester != 'None' THEN 1 END) as test_issues
        FROM gray_data
        WHERE 1=1
        <if test="projectName != null and projectName != ''">
            AND project_name LIKE CONCAT('%', #{projectName}, '%')
        </if>
        <if test="onlineDateStart != null and onlineDateStart != ''">
            AND online_date >= #{onlineDateStart}
        </if>
        <if test="onlineDateEnd != null and onlineDateEnd != ''">
            AND online_date &lt;= #{onlineDateEnd}
        </if>
        GROUP BY project_name
        ORDER BY project_name
    </select>

    <!-- 获取项目开发问题分类统计 -->
    <select id="getProjectDevIssueCategories" resultType="java.util.Map">
        SELECT
            dev_issue_category as category,
            COUNT(*) as count
        FROM gray_data
        WHERE project_name = #{projectName}
          AND dev_contact IS NOT NULL
          AND dev_contact != ''
          AND dev_contact != 'None'
          AND dev_issue_category IS NOT NULL
          AND dev_issue_category != ''
          AND (modify_progress IS NULL OR modify_progress = '' OR (modify_progress != '无需修复' AND modify_progress != '下个版本'))
        GROUP BY dev_issue_category
        ORDER BY
            CASE dev_issue_category
                WHEN '需求理解错误' THEN 1
                WHEN '代码逻辑错误' THEN 2
                WHEN '边界条件处理' THEN 3
                WHEN '异常处理不当' THEN 4
                WHEN '性能问题' THEN 5
                WHEN '接口对接问题' THEN 6
                WHEN '数据库操作问题' THEN 7
                WHEN '配置问题' THEN 8
                WHEN '第三方依赖问题' THEN 9
                WHEN '其他' THEN 10
                ELSE 11
            END,
            dev_issue_category
    </select>

    <!-- 获取项目测试问题分类统计 -->
    <select id="getProjectTestIssueCategories" resultType="java.util.Map">
        SELECT
            test_issue_category as category,
            COUNT(*) as count
        FROM gray_data
        WHERE project_name = #{projectName}
          AND tester IS NOT NULL
          AND tester != ''
          AND tester != 'None'
          AND test_issue_category IS NOT NULL
          AND test_issue_category != ''
          AND (modify_progress IS NULL OR modify_progress = '' OR (modify_progress != '无需修复' AND modify_progress != '下个版本'))
        GROUP BY test_issue_category
        ORDER BY
            CASE test_issue_category
                WHEN '用例设计不充分' THEN 1
                WHEN '测试环境问题' THEN 2
                WHEN '测试数据问题' THEN 3
                WHEN '测试工具问题' THEN 4
                WHEN '测试流程问题' THEN 5
                WHEN '回归测试遗漏' THEN 6
                WHEN '兼容性测试不足' THEN 7
                WHEN '性能测试不足' THEN 8
                WHEN '安全测试不足' THEN 9
                WHEN '其他' THEN 10
                ELSE 11
            END,
            test_issue_category
    </select>

    <!-- 检查项目是否有分析报告 -->
    <select id="checkProjectAnalysisExists" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM project_analysis
        WHERE project_name = #{projectName}
    </select>

    <!-- 获取项目分析报告内容 -->
    <select id="getProjectAnalysisContent" resultType="java.lang.String">
        SELECT analysis_content
        FROM project_analysis
        WHERE project_name = #{projectName}
        LIMIT 1
    </select>

</mapper>
