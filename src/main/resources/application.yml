spring:
  application:
    name: cursor-analysis
  profiles:
    active: dev
  # ===============================
  # ============ Datasource ============
  # ===============================
  datasource:
    # 主数据源
    primary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: *******************************************************************************************************
      username: root
      password: mysql
      hikari:
        pool-name: PrimaryHikariPool
        connection-timeout: 30000
        maximum-pool-size: 10
        minimum-idle: 5
        max-lifetime: 1800000
        idle-timeout: 600000
    # 次数据源
    secondary:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ************************************************************************************************************
      username: root
      password: mysql
      hikari:
        pool-name: SecondaryHikariPool
        connection-timeout: 30000
        maximum-pool-size: 10
        minimum-idle: 5
        max-lifetime: 1800000
        idle-timeout: 600000

  # ===============================
  # ============ thymeleaf ============
  # ===============================
  thymeleaf:
    cache: false # 开发时关闭缓存，方便调试
    prefix: classpath:/templates/
    suffix: .html
    encoding: UTF-8
    mode: HTML

  # ===============================
  # ============ Mybatis ============
  # ===============================
  mybatis:
    # 指定MyBatis的Mapper XML文件位置
    mapper-locations: classpath*:mapper/**/*.xml
    # 自动将数据库字段的下划线命名转换为Java驼峰命名
    configuration:
      map-underscore-to-camel-case: true

  # ===============================
  # ============ Spring MVC ============
  # ===============================
  mvc:
    # 启用隐藏的HTTP方法过滤器，允许使用_method参数来覆盖HTTP方法（如PUT, DELETE）
    hiddenmethod:
      filter:
        enabled: true

  # ===============================
  # ============ Servlet ============
  # ===============================
  servlet:
    multipart:
      # 单个文件最大大小
      max-file-size: 10MB
      # 请求总最大大小
      max-request-size: 10MB
      
  # ===============================
  # ============ LDAP ============
  # ===============================
  ldap:
    urls: ldap://**************:389
    base: ou=howbuy,dc=howbuy,dc=domain
    username: <EMAIL>    # 添加默认管理员账号
    password: Howbuy2007           # 添加默认管理员密码

  # ===============================
  # ============ Security ============
  # ===============================
  security:
    ldap:
      referral: follow

# ===============================
# ============ Server ============
# ===============================
server:
  port: 8083

# ===============================
# ============ logging ============
# ===============================
logging:
  level:
    root: info
    com.example.demo: debug
    org.springframework.security: info
  file:
    name: /data/app/cursor-analysis/logs/application.log
  logback:
    rollingpolicy:
      max-file-size: 10MB
      max-history: 30
      total-size-cap: 1GB
      file-name-pattern: /data/app/cursor-analysis/logs/application.%d{yyyy-MM-dd}.%i.log