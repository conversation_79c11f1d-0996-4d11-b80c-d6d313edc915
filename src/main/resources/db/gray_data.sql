-- 灰度数据表建表语句
-- 作者：hongdong.xie
-- 日期：2025-07-09 16:54:24

CREATE TABLE `gray_data` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_name` varchar(255) NOT NULL COMMENT '项目名',
  `acceptance_env` varchar(100) DEFAULT NULL COMMENT '验收环境',
  `platform` varchar(100) DEFAULT NULL COMMENT '平台',
  `issue_type` varchar(100) DEFAULT NULL COMMENT '问题类型',
  `module` varchar(255) DEFAULT NULL COMMENT '模块',
  `page` varchar(255) DEFAULT NULL COMMENT '页面',
  `issue_description` text COMMENT '问题描述',
  `modify_progress` varchar(100) DEFAULT NULL COMMENT '修改进度',
  `current_testable` varchar(50) DEFAULT NULL COMMENT '当前是否可验',
  `acceptor` varchar(100) DEFAULT NULL COMMENT '验收人',
  `issue_attribution` varchar(255) DEFAULT NULL COMMENT '问题归因',
  `dev_contact` varchar(100) DEFAULT NULL COMMENT '开发对接人',
  `dev_remark` text COMMENT '开发备注',
  `dev_issue_category` varchar(100) DEFAULT NULL COMMENT '开发问题分类',
  `has_test_case` varchar(50) DEFAULT NULL COMMENT '是否有测试用例',
  `test_issue_category` varchar(100) DEFAULT NULL COMMENT '测试问题分类',
  `test_reason_analysis` text COMMENT '测试原因分析',
  `tester` varchar(100) DEFAULT NULL COMMENT '测试人员',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间戳',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_project_name` (`project_name`),
  KEY `idx_acceptance_env` (`acceptance_env`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='灰度数据表';
