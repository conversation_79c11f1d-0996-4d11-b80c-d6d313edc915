create table dev_worktime_spent_segregated
(
    id          bigint auto_increment
        primary key,
    duration    varchar(25)                        null,
    owner       varchar(25)                        null,
    任务类型    varchar(25)                        null,
    effort      decimal                            null,
    team_name   varchar(25)                        null,
    s_team_name varchar(25)                        null,
    main_skill  varchar(25)                        null,
    create_date datetime default CURRENT_TIMESTAMP null
);

create table prod_bugs
(
    ID           int  null,
    标题         text null,
    缺陷分类     text null,
    处理人       text null,
    开发人员     text null,
    测试人员     text null,
    创建人       text null,
    创建时间     text null,
    修复人       text null,
    发现日期     text null,
    缺陷引入日期 text null,
    月份         text null,
    一级分类     text null,
    二级分类     text null,
    constraint prod_bugs_ID_uindex
        unique (ID)
)
comment '产线问题bug';