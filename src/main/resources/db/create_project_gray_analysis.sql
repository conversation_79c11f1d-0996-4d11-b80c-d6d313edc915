-- 项目灰度数据分析表
-- 作者: hongdong.xie
-- 创建时间: 2025-07-10 19:30:35

CREATE TABLE `project_analysis` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `project_name` varchar(255) NOT NULL COMMENT '项目名称',
  `analysis_content` longtext NOT NULL COMMENT '分析内容(MD格式文档)',
  `online_date` varchar(8) DEFAULT NULL COMMENT '项目上线日期yyyyMMdd',
  `gray_date` varchar(8) DEFAULT NULL COMMENT '项目灰度日期yyyyMMdd',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间戳',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间戳',
  PRIMARY KEY (`id`),
  KEY `idx_project_name` (`project_name`),
  KEY `idx_online_date` (`online_date`),
  KEY `idx_gray_date` (`gray_date`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目数据分析表';
