CREATE TABLE `table_sync_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `source_db` varchar(64) NOT NULL COMMENT '源库名称',
  `source_table` varchar(64) NOT NULL COMMENT '源表名称',
  `target_table` varchar(64) NOT NULL COMMENT '目标表名称',
  `sync_type` varchar(32) NOT NULL COMMENT '接入方式',
  `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-无效，1-有效',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_source` (`source_db`,`source_table`),
  KEY `idx_status` (`status`)
) COMMENT='表同步配置表';