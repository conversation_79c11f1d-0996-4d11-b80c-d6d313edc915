package com.example.demo.service;

import com.example.demo.entity.primary.GrayData;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 灰度数据服务测试类
 *
 * <AUTHOR>
 * @date 2025-07-09 16:54:24
 */
@SpringBootTest
@ActiveProfiles("test")
public class GrayDataServiceTest {
    
    @Autowired
    private GrayDataService grayDataService;
    
    @Test
    public void testFindByConditions() {
        // 测试查询功能
        List<GrayData> result = grayDataService.findByConditions(null, null);
        assertNotNull(result);
        
        // 测试项目名模糊查询
        List<GrayData> projectResult = grayDataService.findByConditions("测试", null);
        assertNotNull(projectResult);
        
        // 测试验收环境查询
        List<GrayData> envResult = grayDataService.findByConditions(null, "测试环境");
        assertNotNull(envResult);
    }
    
    @Test
    public void testFindDistinctProjectNames() {
        List<String> projectNames = grayDataService.findDistinctProjectNames();
        assertNotNull(projectNames);
    }
    
    @Test
    public void testFindDistinctAcceptanceEnvs() {
        List<String> acceptanceEnvs = grayDataService.findDistinctAcceptanceEnvs();
        assertNotNull(acceptanceEnvs);
    }
    
    @Test
    public void testExistsByProjectName() {
        // 测试项目名存在性检查
        boolean exists = grayDataService.existsByProjectName("测试项目1");
        // 根据实际数据情况进行断言
        // assertTrue(exists);
        
        boolean notExists = grayDataService.existsByProjectName("不存在的项目");
        assertFalse(notExists);
    }
}
