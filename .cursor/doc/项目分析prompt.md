你是一名资深测试经理和质量分析专家，请使用mysql mcp读取数据库gray_data表，生成一份全面的项目质量分析报告到project_analysis中，报告内容以md格式。分析目标如下：

## 📌 输入数据说明：
- 内容来源于使用mysql mcp读取数据库gray_data表的项目灰度阶段质量统计。
- 表格中包含研发、测试、问题分类等相关字段。
- 表格记录了每条缺陷的责任人、问题分类、模块、阶段、严重程度等关键信息。
- 研发对接人是bug的所属拿个研发
- 测试责任人是改bug对应的需求是由哪个测试负责
- 问题分类，需求优化、需求变更是属于灰度解读产品提出的需求变更，不算研发和测试问题

## 📊 报告内容要求如下：

### 1. 质量数据总览
- 统计项目中总的问题数、按责任人（研发、测试）划分问题数。
- 分别列出【研发的问题】和【测试的问题】数量及占比。
- 各问题的严重等级（致命、高、中、低）分布。

### 2. 研发问题分析
- 列出每位研发对应的问题数量。
- 分类统计研发引入的问题（例如：逻辑错误、数据问题、接口错误、UI问题等）。
- 分析造成研发问题的主要类型、模块分布、影响范围。

### 3. 测试问题分析
- 列出每位测试工程师发现的问题数量。
- 分类统计测试阶段发现的问题类型（如边界缺陷、异常流程遗漏、兼容性问题等）。
- 分析测试过程中暴露出的测试盲区或测试遗漏。

### 4. 模块质量分析
- 统计各系统模块的问题数量及严重程度分布。
- 找出问题最集中的前3个模块，并分析根因。
- 如有复发问题，请指出模块及复发原因。

### 5. 问题分类与趋势
- 问题按照分类进行聚合（功能、接口、数据、性能、UI等）。
- 指出每类问题在各责任人中的分布。
- 若存在某类问题异常偏高，请给出风险提示与建议。

### 6. 质量复盘与改进建议
- 总结本轮版本质量现状，是否达到预期目标。
- 分析质量问题背后的流程或机制缺陷（如：需求不清晰、评审不到位、自测不足等）。
- 针对研发和测试各自提出改进措施和预防建议。

### 📎 输出格式要求：
- 报告格式为 markdown（md）文档，内容清晰分区，包含标题、数据表格、分析文字。
- 每一部分可适当添加可视化建议（如柱状图、饼图、热力图等图示建议）。
- 结尾给出整体质量评级（优秀、良好、一般、需改进）和建议下一版本的质量目标。
- 生成的分析报告保存到project_analysis表中，按照项目名，如果项目已经有报告，则更新

请开始分析并生成报告。